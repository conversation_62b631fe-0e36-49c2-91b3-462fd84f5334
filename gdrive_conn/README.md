# Llama Data Connector

This project connects to Google Drive using LlamaIndex and service account credentials.

## Setup

### Option 1: Using the setup script (macOS/Linux)
```bash
chmod +x setup_venv.sh
./setup_venv.sh
```

### Option 2: Manual setup
1. Create a virtual environment:
   ```bash
   python3 -m venv venv
   ```

2. Activate the virtual environment:
   ```bash
   # On macOS/Linux:
   source venv/bin/activate
   
   # On Windows:
   venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. Make sure your virtual environment is activated:
   ```bash
   source venv/bin/activate
   ```

2. Ensure your service account JSON file (`test_serviceAcc.json`) is in the project directory

3. Update the file IDs in `testConnection.py` with your actual Google Drive file/folder IDs

4. Run the script:
   ```bash
   python testConnection.py
   ```

## Files

- `testConnection.py` - Main script to test Google Drive connection
- `test_serviceAcc.json` - Service account credentials (keep this secure!)
- `requirements.txt` - Python dependencies
- `setup_venv.sh` - Virtual environment setup script

## Notes

- The service account path has been updated to point to `test_serviceAcc.json` in the current directory
- Make sure to replace the placeholder file IDs in `testConnection.py` with actual Google Drive file/folder IDs
- Keep your service account JSON file secure and never commit it to version control
