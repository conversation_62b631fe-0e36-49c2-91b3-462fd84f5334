"""
Main orchestrator class that coordinates the entire Google Drive KG processing pipeline.

This module provides the primary interface for the orchestrator system,
combining drive traversal, file categorization, and knowledge graph creation.
"""

import logging
import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed

from .config.settings import OrchestratorConfig, load_config
from .core.drive_traverser import DriveTraverser
from .core.file_categorizer import FileCategorizer
from .core.category_manager import CategoryManager
from .core.kg_orchestrator import KGOrchestrator
from .models import FileInfo, OrchestratorResult, CategoryProcessingResult
from .utils.drive_auth import DriveAuthenticator
from .utils.progress_tracker import ProgressTracker

logger = logging.getLogger(__name__)


class DriveKGOrchestrator:
    """
    Main orchestrator for Google Drive Knowledge Graph processing.
    
    This class coordinates the entire pipeline:
    1. Authenticate with Google Drive
    2. Traverse folders and discover files
    3. Categorize files based on rules and content
    4. Create knowledge graphs for each category
    5. Track progress and handle errors
    """
    
    def __init__(
        self,
        service_account_path: Optional[str] = None,
        root_folder_id: Optional[str] = None,
        config_path: Optional[str] = None,
        config: Optional[OrchestratorConfig] = None
    ):
        """
        Initialize the orchestrator.
        
        Args:
            service_account_path: Path to Google service account JSON file
            root_folder_id: Google Drive folder ID to start processing from
            config_path: Path to YAML configuration file
            config: Pre-configured OrchestratorConfig object
        """
        # Load configuration
        if config:
            self.config = config
        else:
            self.config = load_config(config_path)
        
        # Override config with provided parameters
        if service_account_path:
            self.config.drive_config.service_account_path = service_account_path
        if root_folder_id:
            self.config.drive_config.root_folder_id = root_folder_id
        
        # Initialize components
        self.authenticator = DriveAuthenticator(self.config.drive_config.service_account_path)
        self.drive_traverser = DriveTraverser(self.config.drive_config, self.authenticator)
        self.file_categorizer = FileCategorizer(self.config.categories)
        self.category_manager = CategoryManager(self.config.categories)
        self.kg_orchestrator = KGOrchestrator(self.config.kg_config)
        
        # Progress tracking
        self.progress_tracker = ProgressTracker()
        
        # Results storage
        self.discovered_files: List[FileInfo] = []
        self.categorized_files: Dict[str, List[FileInfo]] = {}
        self.processing_results: Dict[str, CategoryProcessingResult] = {}
        
        # Setup logging
        self._setup_logging()
        
        # Create output directory
        os.makedirs(self.config.results_output_dir, exist_ok=True)
    
    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f"{self.config.results_output_dir}/orchestrator.log")
            ]
        )
    
    def process_drive_folder(
        self,
        folder_id: Optional[str] = None,
        create_knowledge_graphs: Optional[bool] = None,
        categories_to_process: Optional[List[str]] = None
    ) -> OrchestratorResult:
        """
        Process a Google Drive folder completely.
        
        Args:
            folder_id: Folder ID to process (uses config default if None)
            create_knowledge_graphs: Whether to create KGs (uses config default if None)
            categories_to_process: Specific categories to process (all if None)
            
        Returns:
            OrchestratorResult with complete processing information
        """
        start_time = datetime.now()
        logger.info("Starting Google Drive KG orchestration")
        
        # Use provided parameters or config defaults
        target_folder_id = folder_id or self.config.drive_config.root_folder_id
        should_create_kgs = (
            create_knowledge_graphs 
            if create_knowledge_graphs is not None 
            else self.config.create_knowledge_graphs
        )
        
        try:
            # Step 1: Discover files
            logger.info("Phase 1: Discovering files...")
            self.discovered_files = self.discover_files(target_folder_id)
            
            # Step 2: Categorize files
            logger.info("Phase 2: Categorizing files...")
            self.categorized_files = self.categorize_files(self.discovered_files)
            
            # Step 3: Create knowledge graphs (if enabled)
            if should_create_kgs:
                logger.info("Phase 3: Creating knowledge graphs...")
                self.processing_results = self.create_knowledge_graphs(
                    self.categorized_files,
                    categories_to_process
                )
            
            # Step 4: Generate results
            result = self._generate_final_result(start_time)
            
            # Step 5: Save results
            self._save_results(result)
            
            logger.info("Orchestration completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Orchestration failed: {e}")
            return self._generate_error_result(start_time, str(e))
    
    def discover_files(self, folder_id: Optional[str] = None) -> List[FileInfo]:
        """
        Discover all files in the specified folder.
        
        Args:
            folder_id: Google Drive folder ID
            
        Returns:
            List of discovered FileInfo objects
        """
        logger.info(f"Discovering files in folder: {folder_id or 'root'}")
        
        try:
            files = self.drive_traverser.discover_all_files(folder_id)
            
            # Get content previews for categorization
            logger.info("Extracting content previews...")
            for i, file_info in enumerate(files):
                if i % self.config.progress_update_interval == 0:
                    self.progress_tracker.update_progress(
                        f"Processing file {i+1}/{len(files)}: {file_info.name}",
                        i + 1,
                        len(files)
                    )
                
                # Get content preview for text files
                if file_info.metadata.is_text_file:
                    content_preview = self.drive_traverser.get_file_content_preview(file_info)
                    if content_preview:
                        file_info.content_preview = content_preview
            
            logger.info(f"Discovered {len(files)} files")
            return files
            
        except Exception as e:
            logger.error(f"File discovery failed: {e}")
            raise
    
    def categorize_files(self, files: List[FileInfo]) -> Dict[str, List[FileInfo]]:
        """
        Categorize discovered files.
        
        Args:
            files: List of FileInfo objects to categorize
            
        Returns:
            Dictionary mapping category names to lists of files
        """
        logger.info(f"Categorizing {len(files)} files...")
        
        categorized = {}
        
        for i, file_info in enumerate(files):
            if i % self.config.progress_update_interval == 0:
                self.progress_tracker.update_progress(
                    f"Categorizing file {i+1}/{len(files)}: {file_info.name}",
                    i + 1,
                    len(files)
                )
            
            try:
                # Categorize the file
                categories = self.file_categorizer.categorize_file(file_info)
                
                # Add to categorized collections
                for category in categories:
                    if category not in categorized:
                        categorized[category] = []
                    categorized[category].append(file_info)
                
                # Update file info with categories
                file_info.categories = [cat.name for cat in categories]
                file_info.category_scores = {cat.name: cat.confidence_score for cat in categories}
                file_info.update_processing_status("categorized")
                
            except Exception as e:
                logger.error(f"Failed to categorize file {file_info.name}: {e}")
                file_info.update_processing_status("failed", str(e))
        
        # Log categorization results
        for category, category_files in categorized.items():
            logger.info(f"Category '{category}': {len(category_files)} files")
        
        return categorized
    
    def create_knowledge_graphs(
        self,
        categorized_files: Dict[str, List[FileInfo]],
        categories_to_process: Optional[List[str]] = None
    ) -> Dict[str, CategoryProcessingResult]:
        """
        Create knowledge graphs for categorized files.
        
        Args:
            categorized_files: Dictionary of categorized files
            categories_to_process: Specific categories to process
            
        Returns:
            Dictionary of processing results by category
        """
        # Filter categories if specified
        if categories_to_process:
            categorized_files = {
                cat: files for cat, files in categorized_files.items()
                if cat in categories_to_process
            }
        
        logger.info(f"Creating knowledge graphs for {len(categorized_files)} categories")
        
        results = {}
        
        if self.config.parallel_category_processing:
            # Process categories in parallel
            results = self._process_categories_parallel(categorized_files)
        else:
            # Process categories sequentially
            results = self._process_categories_sequential(categorized_files)
        
        return results
    
    def _process_categories_parallel(
        self, 
        categorized_files: Dict[str, List[FileInfo]]
    ) -> Dict[str, CategoryProcessingResult]:
        """Process categories in parallel."""
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.config.max_category_workers) as executor:
            # Submit all category processing tasks
            future_to_category = {
                executor.submit(self._process_single_category, category, files): category
                for category, files in categorized_files.items()
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_category):
                category = future_to_category[future]
                try:
                    result = future.result()
                    results[category] = result
                    logger.info(f"Completed processing category: {category}")
                except Exception as e:
                    logger.error(f"Failed to process category {category}: {e}")
                    results[category] = CategoryProcessingResult(
                        category_name=category,
                        success=False,
                        error_message=str(e)
                    )
        
        return results
    
    def _process_categories_sequential(
        self,
        categorized_files: Dict[str, List[FileInfo]]
    ) -> Dict[str, CategoryProcessingResult]:
        """Process categories sequentially."""
        results = {}
        
        for category, files in categorized_files.items():
            try:
                logger.info(f"Processing category: {category}")
                result = self._process_single_category(category, files)
                results[category] = result
            except Exception as e:
                logger.error(f"Failed to process category {category}: {e}")
                results[category] = CategoryProcessingResult(
                    category_name=category,
                    success=False,
                    error_message=str(e)
                )
        
        return results
    
    def _process_single_category(
        self,
        category: str,
        files: List[FileInfo]
    ) -> CategoryProcessingResult:
        """Process a single category to create knowledge graph."""
        return self.kg_orchestrator.process_category(category, files)
    
    def _generate_final_result(self, start_time: datetime) -> OrchestratorResult:
        """Generate the final orchestration result."""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Calculate statistics
        total_files = len(self.discovered_files)
        total_categories = len(self.categorized_files)
        successful_categories = sum(
            1 for result in self.processing_results.values() 
            if result.success
        )
        
        return OrchestratorResult(
            success=True,
            total_files=total_files,
            total_categories=total_categories,
            successful_categories=successful_categories,
            processing_time_seconds=duration,
            categories=self.categorized_files,
            processing_results=self.processing_results,
            drive_statistics=self.drive_traverser.get_statistics(),
            start_time=start_time,
            end_time=end_time
        )
    
    def _generate_error_result(self, start_time: datetime, error_message: str) -> OrchestratorResult:
        """Generate an error result."""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return OrchestratorResult(
            success=False,
            error_message=error_message,
            total_files=len(self.discovered_files),
            total_categories=len(self.categorized_files),
            processing_time_seconds=duration,
            categories=self.categorized_files,
            processing_results=self.processing_results,
            start_time=start_time,
            end_time=end_time
        )
    
    def _save_results(self, result: OrchestratorResult):
        """Save processing results to files."""
        if not self.config.save_intermediate_results:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save main result
        result_file = f"{self.config.results_output_dir}/orchestration_result_{timestamp}.json"
        with open(result_file, 'w') as f:
            json.dump(result.to_dict(), f, indent=2, default=str)
        
        # Save file discovery results
        files_file = f"{self.config.results_output_dir}/discovered_files_{timestamp}.json"
        with open(files_file, 'w') as f:
            json.dump([file.to_dict() for file in self.discovered_files], f, indent=2, default=str)
        
        # Save categorization results
        categories_file = f"{self.config.results_output_dir}/categorized_files_{timestamp}.json"
        categorization_data = {
            category: [file.to_dict() for file in files]
            for category, files in self.categorized_files.items()
        }
        with open(categories_file, 'w') as f:
            json.dump(categorization_data, f, indent=2, default=str)
        
        logger.info(f"Results saved to {self.config.results_output_dir}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the orchestration."""
        return {
            "discovery": self.drive_traverser.get_statistics(),
            "categorization": self.file_categorizer.get_statistics(),
            "knowledge_graphs": {
                category: result.to_dict()
                for category, result in self.processing_results.items()
            },
            "summary": {
                "total_files_discovered": len(self.discovered_files),
                "total_categories": len(self.categorized_files),
                "categories_processed": len(self.processing_results),
                "successful_kg_creations": sum(
                    1 for result in self.processing_results.values() 
                    if result.success
                )
            }
        }
