"""
Google Drive Knowledge Graph Orchestrator

A comprehensive system that traverses Google Drive folders, categorizes files,
and creates knowledge graphs for each category using the enterprise_kg_minimal system.

Main Components:
- DriveTraverser: Recursively explores Google Drive folders
- FileCategorizer: Classifies files by type, content, and metadata  
- CategoryManager: Organizes files into logical categories
- KGOrchestrator: Coordinates knowledge graph creation

Usage:
    from gdrive_kg_orchestrator import DriveKGOrchestrator
    
    orchestrator = DriveKGOrchestrator(
        service_account_path="config/service_account.json",
        root_folder_id="your_folder_id"
    )
    
    result = orchestrator.process_drive_folder()
"""

from .main_orchestrator import DriveKGOrchestrator
from .config.settings import OrchestratorConfig, DriveConfig, KGConfig, CategoryConfig
from .models import FileInfo, CategoryResult, OrchestratorResult

__version__ = "1.0.0"

__all__ = [
    "DriveKGOrchestrator",
    "OrchestratorConfig",
    "DriveConfig", 
    "KGConfig",
    "CategoryConfig",
    "FileInfo",
    "CategoryResult",
    "OrchestratorResult"
]
