# Google Drive KG Orchestrator Requirements
# Install with: pip install -r requirements.txt

# Google Drive API and Authentication
google-api-python-client>=2.0.0
google-auth>=2.0.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.2.0

# LlamaIndex for document processing (optional, for content analysis)
llama-index>=0.9.0
llama-index-readers-google>=0.1.0

# Neo4j and Knowledge Graph dependencies
neo4j>=5.0.0
python-dotenv>=1.0.0

# LLM providers (install at least one)
openai>=1.0.0          # For OpenAI GPT models
anthropic>=0.7.0       # For Anthropic Claude models

# File processing and content analysis
pypdf>=3.0.0           # PDF processing
python-docx>=0.8.11    # DOCX processing
openpyxl>=3.1.0        # Excel processing
python-pptx>=0.6.21    # PowerPoint processing

# Data processing and utilities
pyyaml>=6.0            # YAML configuration files
pandas>=2.0.0          # Data manipulation
numpy>=1.24.0          # Numerical operations
tqdm>=4.65.0           # Progress bars

# Async and parallel processing
asyncio-throttle>=1.0.2
aiofiles>=23.0.0
concurrent-futures>=3.1.1

# Content analysis and NLP (optional)
nltk>=3.8              # Natural language processing
spacy>=3.7.0           # Advanced NLP (optional)
textstat>=0.7.3        # Text statistics

# Logging and monitoring
structlog>=23.0.0      # Structured logging
rich>=13.0.0           # Rich console output

# Testing and development
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
black>=23.0.0          # Code formatting
flake8>=6.0.0          # Linting

# Optional: Advanced file type support
# python-magic>=0.4.27   # File type detection
# pillow>=10.0.0         # Image processing
# moviepy>=1.0.3         # Video processing
