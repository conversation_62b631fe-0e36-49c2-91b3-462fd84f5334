# File Categorization Rules for Google Drive KG Orchestrator
# This file defines how files should be categorized for knowledge graph processing

categories:
  # Technical Documentation
  technical_docs:
    description: "Technical documentation, API docs, system specifications"
    file_extensions: [".md", ".rst", ".txt", ".adoc"]
    content_keywords: 
      - "API"
      - "documentation"
      - "technical"
      - "specification"
      - "architecture"
      - "system design"
      - "developer"
      - "implementation"
    folder_patterns:
      - "**/docs/**"
      - "**/documentation/**"
      - "**/technical/**"
      - "**/api/**"
      - "**/specs/**"
    exclude_patterns:
      - "**/temp/**"
      - "**/draft/**"
    min_file_size: 100
    priority: 1
    kg_config_override:
      chunking_strategy: "semantic_based"
      chunk_size: 1200
    enabled: true

  # Business Reports and Documents
  business_reports:
    description: "Business reports, proposals, strategic documents"
    file_extensions: [".pdf", ".docx", ".doc"]
    content_keywords:
      - "report"
      - "analysis"
      - "business"
      - "strategy"
      - "proposal"
      - "executive"
      - "summary"
      - "quarterly"
      - "annual"
      - "financial"
    folder_patterns:
      - "**/reports/**"
      - "**/business/**"
      - "**/strategy/**"
      - "**/executive/**"
      - "**/proposals/**"
    min_file_size: 1000
    priority: 2
    kg_config_override:
      chunking_strategy: "paragraph_based"
      chunk_size: 1500
    enabled: true

  # Presentations
  presentations:
    description: "Presentation files and slide decks"
    file_extensions: [".pptx", ".ppt", ".pdf"]
    content_keywords:
      - "presentation"
      - "slides"
      - "deck"
      - "meeting"
      - "pitch"
      - "demo"
    folder_patterns:
      - "**/presentations/**"
      - "**/slides/**"
      - "**/decks/**"
      - "**/meetings/**"
    min_file_size: 500
    priority: 3
    kg_config_override:
      chunking_strategy: "fixed_size"
      chunk_size: 800
    enabled: true

  # Data and Spreadsheets
  data_files:
    description: "Spreadsheets, CSV files, and data documents"
    file_extensions: [".xlsx", ".xls", ".csv", ".tsv"]
    content_keywords:
      - "data"
      - "spreadsheet"
      - "analysis"
      - "metrics"
      - "dashboard"
      - "report"
    folder_patterns:
      - "**/data/**"
      - "**/analytics/**"
      - "**/metrics/**"
      - "**/dashboards/**"
    min_file_size: 100
    priority: 4
    kg_config_override:
      chunking_strategy: "hybrid"
      chunk_size: 600
    enabled: true

  # Meeting Notes and Minutes
  meeting_notes:
    description: "Meeting notes, minutes, and discussion records"
    file_extensions: [".md", ".txt", ".docx", ".pdf"]
    content_keywords:
      - "meeting"
      - "minutes"
      - "notes"
      - "discussion"
      - "agenda"
      - "action items"
      - "decisions"
      - "attendees"
    folder_patterns:
      - "**/meetings/**"
      - "**/notes/**"
      - "**/minutes/**"
      - "**/discussions/**"
    min_file_size: 200
    priority: 5
    kg_config_override:
      chunking_strategy: "sentence_based"
      chunk_size: 1000
    enabled: true

  # Project Documentation
  project_docs:
    description: "Project-specific documentation and files"
    file_extensions: [".md", ".pdf", ".docx", ".txt"]
    content_keywords:
      - "project"
      - "requirements"
      - "scope"
      - "timeline"
      - "deliverables"
      - "milestone"
      - "status"
    folder_patterns:
      - "**/projects/**"
      - "**/project-*/**"
      - "**/proj_*/**"
    min_file_size: 300
    priority: 6
    kg_config_override:
      chunking_strategy: "hybrid"
      chunk_size: 1100
    enabled: true

  # Research and Academic Papers
  research_papers:
    description: "Research papers, academic documents, whitepapers"
    file_extensions: [".pdf", ".docx", ".tex"]
    content_keywords:
      - "research"
      - "paper"
      - "study"
      - "analysis"
      - "whitepaper"
      - "academic"
      - "journal"
      - "conference"
      - "abstract"
      - "methodology"
    folder_patterns:
      - "**/research/**"
      - "**/papers/**"
      - "**/academic/**"
      - "**/whitepapers/**"
    min_file_size: 2000
    priority: 7
    kg_config_override:
      chunking_strategy: "semantic_based"
      chunk_size: 1800
    enabled: true

  # Code Documentation
  code_docs:
    description: "Code documentation, README files, API references"
    file_extensions: [".md", ".rst", ".txt"]
    content_keywords:
      - "code"
      - "API"
      - "function"
      - "class"
      - "method"
      - "library"
      - "framework"
      - "installation"
      - "usage"
      - "example"
    folder_patterns:
      - "**/code/**"
      - "**/src/**"
      - "**/lib/**"
      - "**/api/**"
      - "**/README*"
    min_file_size: 100
    priority: 8
    kg_config_override:
      chunking_strategy: "hybrid"
      chunk_size: 900
    enabled: true

  # Training and Educational Materials
  training_materials:
    description: "Training documents, tutorials, educational content"
    file_extensions: [".pdf", ".docx", ".pptx", ".md"]
    content_keywords:
      - "training"
      - "tutorial"
      - "guide"
      - "course"
      - "lesson"
      - "education"
      - "learning"
      - "workshop"
      - "certification"
    folder_patterns:
      - "**/training/**"
      - "**/tutorials/**"
      - "**/education/**"
      - "**/courses/**"
      - "**/workshops/**"
    min_file_size: 500
    priority: 9
    kg_config_override:
      chunking_strategy: "paragraph_based"
      chunk_size: 1300
    enabled: true

  # Legal and Compliance Documents
  legal_docs:
    description: "Legal documents, contracts, compliance materials"
    file_extensions: [".pdf", ".docx", ".doc"]
    content_keywords:
      - "legal"
      - "contract"
      - "agreement"
      - "compliance"
      - "policy"
      - "terms"
      - "conditions"
      - "privacy"
      - "gdpr"
      - "regulation"
    folder_patterns:
      - "**/legal/**"
      - "**/contracts/**"
      - "**/compliance/**"
      - "**/policies/**"
    min_file_size: 1000
    priority: 10
    kg_config_override:
      chunking_strategy: "paragraph_based"
      chunk_size: 1600
    enabled: true

# Default category for uncategorized files
default_category:
  name: "miscellaneous"
  description: "Files that don't match any specific category"
  priority: 999
  kg_config_override:
    chunking_strategy: "hybrid"
    chunk_size: 1000
  enabled: true

# Global categorization settings
categorization_settings:
  # Minimum confidence score for content-based categorization
  min_confidence_score: 0.6
  
  # Maximum number of categories a file can belong to
  max_categories_per_file: 2
  
  # Whether to use content analysis for categorization (requires LLM)
  use_content_analysis: true
  
  # Content analysis sample size (characters to analyze)
  content_sample_size: 2000
  
  # Whether to analyze folder structure for categorization
  use_folder_structure: true
  
  # Whether to use file metadata for categorization
  use_file_metadata: true
