"""
Configuration settings for Google Drive KG Orchestrator.

This module defines configuration classes and handles environment variables,
YAML configuration files, and default settings.
"""

import os
import yaml
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from pathlib import Path


@dataclass
class DriveConfig:
    """Configuration for Google Drive access."""
    service_account_path: str
    root_folder_id: Optional[str] = None
    max_depth: int = 10
    max_files_per_folder: int = 1000
    rate_limit_requests_per_second: float = 10.0
    batch_size: int = 100
    include_shared_drives: bool = False
    file_size_limit_mb: int = 100
    excluded_extensions: List[str] = field(default_factory=lambda: ['.tmp', '.log', '.cache'])
    included_extensions: List[str] = field(default_factory=list)


@dataclass
class KGConfig:
    """Configuration for Knowledge Graph processing."""
    neo4j_uri: str = "bolt://localhost:7687"
    neo4j_user: str = "neo4j"
    neo4j_password: str = "password"
    neo4j_database: Optional[str] = None
    llm_provider: str = "openai"
    llm_model: str = "gpt-4o"
    llm_api_key: Optional[str] = None
    chunking_strategy: str = "hybrid"
    chunk_size: int = 1000
    chunk_overlap: int = 200
    parallel_processing: bool = True
    max_workers: int = 4
    batch_processing: bool = True
    batch_size: int = 10


@dataclass
class CategoryConfig:
    """Configuration for file categorization."""
    name: str
    description: str
    file_extensions: List[str] = field(default_factory=list)
    content_keywords: List[str] = field(default_factory=list)
    folder_patterns: List[str] = field(default_factory=list)
    exclude_patterns: List[str] = field(default_factory=list)
    min_file_size: int = 0
    max_file_size: Optional[int] = None
    priority: int = 1
    kg_config_override: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True


@dataclass
class OrchestratorConfig:
    """Main configuration for the orchestrator."""
    drive_config: DriveConfig
    kg_config: KGConfig
    categories: Dict[str, CategoryConfig] = field(default_factory=dict)
    
    # Processing options
    create_knowledge_graphs: bool = True
    parallel_category_processing: bool = True
    max_category_workers: int = 2
    
    # Monitoring and logging
    log_level: str = "INFO"
    progress_update_interval: int = 10
    save_intermediate_results: bool = True
    results_output_dir: str = "results"
    
    # Error handling
    max_retries: int = 3
    retry_delay_seconds: float = 1.0
    continue_on_error: bool = True
    
    @classmethod
    def from_env(cls) -> 'OrchestratorConfig':
        """Create configuration from environment variables."""
        drive_config = DriveConfig(
            service_account_path=os.getenv("GOOGLE_SERVICE_ACCOUNT_PATH", "config/service_account.json"),
            root_folder_id=os.getenv("GOOGLE_DRIVE_ROOT_FOLDER_ID"),
            max_depth=int(os.getenv("DRIVE_MAX_DEPTH", "10")),
            rate_limit_requests_per_second=float(os.getenv("DRIVE_RATE_LIMIT", "10.0")),
            file_size_limit_mb=int(os.getenv("DRIVE_FILE_SIZE_LIMIT_MB", "100"))
        )
        
        kg_config = KGConfig(
            neo4j_uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            neo4j_user=os.getenv("NEO4J_USER", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD", "password"),
            neo4j_database=os.getenv("NEO4J_DATABASE"),
            llm_provider=os.getenv("LLM_PROVIDER", "openai"),
            llm_model=os.getenv("LLM_MODEL", "gpt-4o"),
            llm_api_key=os.getenv("OPENAI_API_KEY") or os.getenv("ANTHROPIC_API_KEY"),
            chunking_strategy=os.getenv("CHUNKING_STRATEGY", "hybrid"),
            chunk_size=int(os.getenv("CHUNK_SIZE", "1000")),
            chunk_overlap=int(os.getenv("CHUNK_OVERLAP", "200")),
            max_workers=int(os.getenv("KG_MAX_WORKERS", "4"))
        )
        
        return cls(
            drive_config=drive_config,
            kg_config=kg_config,
            create_knowledge_graphs=os.getenv("CREATE_KNOWLEDGE_GRAPHS", "true").lower() == "true",
            parallel_category_processing=os.getenv("PARALLEL_CATEGORY_PROCESSING", "true").lower() == "true",
            max_category_workers=int(os.getenv("MAX_CATEGORY_WORKERS", "2")),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            results_output_dir=os.getenv("RESULTS_OUTPUT_DIR", "results")
        )
    
    @classmethod
    def from_yaml(cls, yaml_path: str) -> 'OrchestratorConfig':
        """Create configuration from YAML file."""
        with open(yaml_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # Parse drive config
        drive_data = config_data.get('drive', {})
        drive_config = DriveConfig(**drive_data)
        
        # Parse KG config
        kg_data = config_data.get('knowledge_graph', {})
        kg_config = KGConfig(**kg_data)
        
        # Parse categories
        categories = {}
        for name, cat_data in config_data.get('categories', {}).items():
            categories[name] = CategoryConfig(name=name, **cat_data)
        
        # Parse main config
        main_config = config_data.get('orchestrator', {})
        
        return cls(
            drive_config=drive_config,
            kg_config=kg_config,
            categories=categories,
            **main_config
        )
    
    def load_categories_from_yaml(self, categories_yaml_path: str):
        """Load category definitions from YAML file."""
        if not os.path.exists(categories_yaml_path):
            return
            
        with open(categories_yaml_path, 'r') as f:
            categories_data = yaml.safe_load(f)
        
        for name, cat_data in categories_data.get('categories', {}).items():
            self.categories[name] = CategoryConfig(name=name, **cat_data)
    
    def save_to_yaml(self, yaml_path: str):
        """Save configuration to YAML file."""
        config_data = {
            'drive': {
                'service_account_path': self.drive_config.service_account_path,
                'root_folder_id': self.drive_config.root_folder_id,
                'max_depth': self.drive_config.max_depth,
                'max_files_per_folder': self.drive_config.max_files_per_folder,
                'rate_limit_requests_per_second': self.drive_config.rate_limit_requests_per_second,
                'batch_size': self.drive_config.batch_size,
                'file_size_limit_mb': self.drive_config.file_size_limit_mb,
                'excluded_extensions': self.drive_config.excluded_extensions,
                'included_extensions': self.drive_config.included_extensions
            },
            'knowledge_graph': {
                'neo4j_uri': self.kg_config.neo4j_uri,
                'neo4j_user': self.kg_config.neo4j_user,
                'neo4j_password': self.kg_config.neo4j_password,
                'neo4j_database': self.kg_config.neo4j_database,
                'llm_provider': self.kg_config.llm_provider,
                'llm_model': self.kg_config.llm_model,
                'chunking_strategy': self.kg_config.chunking_strategy,
                'chunk_size': self.kg_config.chunk_size,
                'chunk_overlap': self.kg_config.chunk_overlap,
                'parallel_processing': self.kg_config.parallel_processing,
                'max_workers': self.kg_config.max_workers
            },
            'categories': {
                name: {
                    'description': cat.description,
                    'file_extensions': cat.file_extensions,
                    'content_keywords': cat.content_keywords,
                    'folder_patterns': cat.folder_patterns,
                    'exclude_patterns': cat.exclude_patterns,
                    'min_file_size': cat.min_file_size,
                    'max_file_size': cat.max_file_size,
                    'priority': cat.priority,
                    'kg_config_override': cat.kg_config_override,
                    'enabled': cat.enabled
                }
                for name, cat in self.categories.items()
            },
            'orchestrator': {
                'create_knowledge_graphs': self.create_knowledge_graphs,
                'parallel_category_processing': self.parallel_category_processing,
                'max_category_workers': self.max_category_workers,
                'log_level': self.log_level,
                'progress_update_interval': self.progress_update_interval,
                'save_intermediate_results': self.save_intermediate_results,
                'results_output_dir': self.results_output_dir,
                'max_retries': self.max_retries,
                'retry_delay_seconds': self.retry_delay_seconds,
                'continue_on_error': self.continue_on_error
            }
        }
        
        with open(yaml_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False, indent=2)


def load_config(config_path: Optional[str] = None) -> OrchestratorConfig:
    """
    Load configuration from various sources.
    
    Priority order:
    1. YAML file (if provided)
    2. Environment variables
    3. Default values
    """
    if config_path and os.path.exists(config_path):
        config = OrchestratorConfig.from_yaml(config_path)
    else:
        config = OrchestratorConfig.from_env()
    
    # Load categories from default location if exists
    categories_path = "config/categories.yaml"
    if os.path.exists(categories_path):
        config.load_categories_from_yaml(categories_path)
    
    return config
