"""
Basic usage example for Google Drive KG Orchestrator.

This example demonstrates how to:
1. Set up the orchestrator with basic configuration
2. Traverse a Google Drive folder
3. Categorize discovered files
4. Create knowledge graphs for each category
5. Review and export results
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import the orchestrator
sys.path.append(str(Path(__file__).parent.parent))

from gdrive_kg_orchestrator import DriveKGOrchestrator
from gdrive_kg_orchestrator.config.settings import OrchestratorConfig, DriveConfig, KGConfig


def basic_example():
    """Basic orchestrator usage example."""
    print("🚀 Google Drive KG Orchestrator - Basic Usage Example")
    print("=" * 60)
    
    # Step 1: Set up configuration
    print("\n📋 Step 1: Configuration Setup")
    
    # Option A: Use environment variables (recommended)
    if os.getenv("GOOGLE_SERVICE_ACCOUNT_PATH") and os.getenv("GOOGLE_DRIVE_ROOT_FOLDER_ID"):
        print("✅ Using environment variables for configuration")
        orchestrator = DriveKGOrchestrator()
    
    # Option B: Provide configuration directly
    else:
        print("⚙️ Using direct configuration")
        
        # You need to provide these values
        SERVICE_ACCOUNT_PATH = "config/service_account.json"  # Path to your service account JSON
        ROOT_FOLDER_ID = "1isEQO_RZJD_T-AzUXs9Ipl1mgfbmRpRK"  # Your Google Drive folder ID
        
        orchestrator = DriveKGOrchestrator(
            service_account_path=SERVICE_ACCOUNT_PATH,
            root_folder_id=ROOT_FOLDER_ID
        )
    
    # Step 2: Process the Google Drive folder
    print("\n🔍 Step 2: Processing Google Drive Folder")
    print("This will:")
    print("  - Traverse the folder structure")
    print("  - Discover and categorize files")
    print("  - Create knowledge graphs for each category")
    
    try:
        result = orchestrator.process_drive_folder()
        
        if result.success:
            print("\n✅ Processing completed successfully!")
            print_results_summary(result)
        else:
            print(f"\n❌ Processing failed: {result.error_message}")
            
    except Exception as e:
        print(f"\n💥 Error during processing: {e}")
        print("Please check your configuration and try again.")


def print_results_summary(result):
    """Print a summary of the processing results."""
    print("\n📊 Processing Results Summary")
    print("-" * 40)
    
    # Overall statistics
    print(f"📁 Total files discovered: {result.total_files}")
    print(f"📂 Categories identified: {result.total_categories}")
    print(f"⏱️  Processing time: {result.processing_time_seconds:.1f} seconds")
    print(f"✅ Successful categories: {result.successful_categories}")
    
    # Category breakdown
    print(f"\n📋 Category Breakdown:")
    for category, files in result.categories.items():
        print(f"  • {category}: {len(files)} files")
    
    # Knowledge graph results
    if result.processing_results:
        print(f"\n🧠 Knowledge Graph Results:")
        for category, kg_result in result.processing_results.items():
            if kg_result.success:
                print(f"  ✅ {category}:")
                print(f"     - Chunks created: {kg_result.total_chunks}")
                print(f"     - Entities extracted: {kg_result.total_entities}")
                print(f"     - Relationships: {kg_result.total_relationships}")
            else:
                print(f"  ❌ {category}: {kg_result.error_message}")
    
    # File examples
    print(f"\n📄 Example Files by Category:")
    for category, files in list(result.categories.items())[:3]:  # Show first 3 categories
        print(f"  📂 {category}:")
        for file in files[:3]:  # Show first 3 files
            print(f"     - {file.name} ({file.size_mb:.1f} MB)")
        if len(files) > 3:
            print(f"     ... and {len(files) - 3} more files")


def advanced_example():
    """Advanced orchestrator usage with custom configuration."""
    print("\n🔧 Advanced Usage Example")
    print("=" * 40)
    
    # Custom configuration
    config = OrchestratorConfig(
        drive_config=DriveConfig(
            service_account_path="config/service_account.json",
            root_folder_id="your_folder_id",
            max_depth=5,  # Limit traversal depth
            file_size_limit_mb=25,  # Skip files larger than 25MB
            rate_limit_requests_per_second=5.0  # Slower API calls
        ),
        kg_config=KGConfig(
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="your_password",
            llm_provider="anthropic",
            llm_model="claude-3-5-sonnet-latest",
            chunking_strategy="semantic_based",
            chunk_size=1200,
            parallel_processing=True,
            max_workers=2
        ),
        create_knowledge_graphs=True,
        parallel_category_processing=True,
        max_category_workers=2,
        log_level="DEBUG"
    )
    
    orchestrator = DriveKGOrchestrator(config=config)
    
    # Process only specific categories
    result = orchestrator.process_drive_folder(
        create_knowledge_graphs=True,
        categories_to_process=["technical_docs", "business_reports"]
    )
    
    if result.success:
        print("✅ Advanced processing completed!")
        print_results_summary(result)


def category_specific_example():
    """Example of processing specific categories only."""
    print("\n🎯 Category-Specific Processing Example")
    print("=" * 45)
    
    orchestrator = DriveKGOrchestrator(
        service_account_path="config/service_account.json",
        root_folder_id="your_folder_id"
    )
    
    # Step 1: Discover and categorize files (without creating KGs)
    print("🔍 Discovering and categorizing files...")
    files = orchestrator.discover_files()
    categorized = orchestrator.categorize_files(files)
    
    # Step 2: Review categories and select which ones to process
    print("\n📋 Available categories:")
    for category, files in categorized.items():
        print(f"  • {category}: {len(files)} files")
    
    # Step 3: Process only high-priority categories
    high_priority_categories = ["technical_docs", "business_reports", "research_papers"]
    available_categories = [cat for cat in high_priority_categories if cat in categorized]
    
    if available_categories:
        print(f"\n🎯 Processing categories: {available_categories}")
        kg_results = orchestrator.create_knowledge_graphs(
            categorized,
            categories_to_process=available_categories
        )
        
        # Review results
        for category, result in kg_results.items():
            if result.success:
                print(f"✅ {category}: {result.total_entities} entities, {result.total_relationships} relationships")
            else:
                print(f"❌ {category}: {result.error_message}")
    else:
        print("⚠️ No high-priority categories found in this folder")


def export_results_example():
    """Example of exporting results to different formats."""
    print("\n📤 Export Results Example")
    print("=" * 30)
    
    orchestrator = DriveKGOrchestrator()
    result = orchestrator.process_drive_folder()
    
    if result.success:
        # Results are automatically saved to JSON files
        print("✅ Results automatically saved to:")
        print("  - orchestration_result_TIMESTAMP.json")
        print("  - discovered_files_TIMESTAMP.json") 
        print("  - categorized_files_TIMESTAMP.json")
        
        # Get statistics for custom reporting
        stats = orchestrator.get_statistics()
        print(f"\n📊 Statistics available:")
        print(f"  - Discovery: {len(stats['discovery'])} metrics")
        print(f"  - Categorization: {len(stats['categorization'])} metrics")
        print(f"  - Knowledge Graphs: {len(stats['knowledge_graphs'])} categories")


if __name__ == "__main__":
    print("🎯 Choose an example to run:")
    print("1. Basic Usage")
    print("2. Advanced Configuration")
    print("3. Category-Specific Processing")
    print("4. Export Results")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == "1":
        basic_example()
    elif choice == "2":
        advanced_example()
    elif choice == "3":
        category_specific_example()
    elif choice == "4":
        export_results_example()
    else:
        print("Invalid choice. Running basic example...")
        basic_example()
    
    print("\n🎉 Example completed!")
    print("\nNext steps:")
    print("1. Check the 'results/' directory for output files")
    print("2. Review the Neo4j database for created knowledge graphs")
    print("3. Customize categories in 'config/categories.yaml'")
    print("4. Adjust configuration in environment variables or config files")
