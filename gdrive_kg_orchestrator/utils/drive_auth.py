"""
Google Drive Authentication module for Google Drive KG Orchestrator.

This module provides authentication functionality for Google Drive API access
using service account credentials.
"""

import logging
import json
import os
from typing import Optional
from google.oauth2 import service_account
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

logger = logging.getLogger(__name__)


class DriveAuthenticator:
    """
    Handles Google Drive API authentication using service account credentials.
    
    This class manages:
    1. Service account credential loading and validation
    2. Google Drive API service initialization
    3. Credential refresh and error handling
    4. Scope management for Drive access
    """
    
    # Required scopes for Google Drive access
    SCOPES = [
        'https://www.googleapis.com/auth/drive.readonly',
        'https://www.googleapis.com/auth/drive.metadata.readonly'
    ]
    
    def __init__(self, service_account_path: str):
        """
        Initialize the Drive authenticator.
        
        Args:
            service_account_path: Path to the service account JSON file
        """
        self.service_account_path = service_account_path
        self.credentials = None
        self.service = None
        
        # Validate service account file
        self._validate_service_account_file()
        
        logger.info(f"Initialized DriveAuthenticator with service account: {service_account_path}")
    
    def _validate_service_account_file(self):
        """Validate that the service account file exists and is valid."""
        if not os.path.exists(self.service_account_path):
            raise FileNotFoundError(f"Service account file not found: {self.service_account_path}")
        
        try:
            with open(self.service_account_path, 'r') as f:
                service_account_info = json.load(f)
            
            # Check required fields
            required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
            missing_fields = [field for field in required_fields if field not in service_account_info]
            
            if missing_fields:
                raise ValueError(f"Service account file missing required fields: {missing_fields}")
            
            if service_account_info.get('type') != 'service_account':
                raise ValueError("Invalid service account file: type must be 'service_account'")
            
            logger.info("Service account file validation successful")
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in service account file: {e}")
        except Exception as e:
            raise ValueError(f"Error validating service account file: {e}")
    
    def get_credentials(self):
        """
        Get authenticated credentials for Google Drive API.
        
        Returns:
            Google service account credentials
        """
        if self.credentials is None:
            try:
                self.credentials = service_account.Credentials.from_service_account_file(
                    self.service_account_path,
                    scopes=self.SCOPES
                )
                logger.info("Successfully loaded service account credentials")
                
            except Exception as e:
                logger.error(f"Failed to load service account credentials: {e}")
                raise
        
        # Refresh credentials if needed
        if self.credentials.expired:
            try:
                self.credentials.refresh(Request())
                logger.info("Refreshed expired credentials")
            except Exception as e:
                logger.error(f"Failed to refresh credentials: {e}")
                raise
        
        return self.credentials
    
    def get_drive_service(self):
        """
        Get authenticated Google Drive service.
        
        Returns:
            Google Drive API service object
        """
        if self.service is None:
            try:
                credentials = self.get_credentials()
                self.service = build('drive', 'v3', credentials=credentials)
                logger.info("Successfully initialized Google Drive service")
                
            except Exception as e:
                logger.error(f"Failed to initialize Google Drive service: {e}")
                raise
        
        return self.service
    
    def test_connection(self) -> dict:
        """
        Test the connection to Google Drive API.
        
        Returns:
            Dictionary with connection test results
        """
        result = {
            "success": False,
            "error": None,
            "user_info": None,
            "permissions": []
        }
        
        try:
            service = self.get_drive_service()
            
            # Test basic API access
            about = service.about().get(fields="user,storageQuota").execute()
            result["user_info"] = about.get("user", {})
            result["storage_quota"] = about.get("storageQuota", {})
            
            # Test file listing (just get first few files)
            files_result = service.files().list(
                pageSize=5,
                fields="files(id,name,mimeType)"
            ).execute()
            
            result["sample_files"] = files_result.get("files", [])
            result["success"] = True
            
            logger.info("Google Drive connection test successful")
            
        except HttpError as e:
            error_msg = f"HTTP error {e.resp.status}: {e.error_details}"
            result["error"] = error_msg
            logger.error(f"Google Drive connection test failed: {error_msg}")
            
        except Exception as e:
            error_msg = f"Connection test failed: {str(e)}"
            result["error"] = error_msg
            logger.error(error_msg)
        
        return result
    
    def get_user_info(self) -> Optional[dict]:
        """
        Get information about the authenticated user/service account.
        
        Returns:
            Dictionary with user information or None if failed
        """
        try:
            service = self.get_drive_service()
            about = service.about().get(fields="user").execute()
            return about.get("user", {})
            
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            return None
    
    def validate_folder_access(self, folder_id: str) -> dict:
        """
        Validate access to a specific Google Drive folder.
        
        Args:
            folder_id: Google Drive folder ID to test
            
        Returns:
            Dictionary with validation results
        """
        result = {
            "success": False,
            "error": None,
            "folder_info": None,
            "can_read": False,
            "file_count": 0
        }
        
        try:
            service = self.get_drive_service()
            
            # Get folder information
            folder_info = service.files().get(
                fileId=folder_id,
                fields="id,name,mimeType,parents,permissions"
            ).execute()
            
            result["folder_info"] = {
                "id": folder_info.get("id"),
                "name": folder_info.get("name"),
                "mimeType": folder_info.get("mimeType"),
                "parents": folder_info.get("parents", [])
            }
            
            # Check if it's actually a folder
            if folder_info.get("mimeType") != "application/vnd.google-apps.folder":
                result["error"] = "Specified ID is not a folder"
                return result
            
            # Test file listing in folder
            files_result = service.files().list(
                q=f"'{folder_id}' in parents",
                pageSize=10,
                fields="files(id,name,mimeType)"
            ).execute()
            
            result["file_count"] = len(files_result.get("files", []))
            result["can_read"] = True
            result["success"] = True
            
            logger.info(f"Successfully validated access to folder: {folder_info.get('name')}")
            
        except HttpError as e:
            if e.resp.status == 404:
                result["error"] = "Folder not found or no access"
            elif e.resp.status == 403:
                result["error"] = "Access denied to folder"
            else:
                result["error"] = f"HTTP error {e.resp.status}: {e.error_details}"
            
            logger.error(f"Folder access validation failed: {result['error']}")
            
        except Exception as e:
            result["error"] = f"Validation failed: {str(e)}"
            logger.error(result["error"])
        
        return result
    
    def refresh_credentials(self):
        """Force refresh of credentials."""
        if self.credentials:
            try:
                self.credentials.refresh(Request())
                logger.info("Credentials refreshed successfully")
            except Exception as e:
                logger.error(f"Failed to refresh credentials: {e}")
                raise
        else:
            logger.warning("No credentials to refresh")
    
    def get_auth_status(self) -> dict:
        """
        Get current authentication status.
        
        Returns:
            Dictionary with authentication status information
        """
        status = {
            "credentials_loaded": self.credentials is not None,
            "credentials_valid": False,
            "credentials_expired": False,
            "service_initialized": self.service is not None,
            "scopes": self.SCOPES
        }
        
        if self.credentials:
            status["credentials_valid"] = self.credentials.valid
            status["credentials_expired"] = self.credentials.expired
            
            if hasattr(self.credentials, 'service_account_email'):
                status["service_account_email"] = self.credentials.service_account_email
        
        return status
