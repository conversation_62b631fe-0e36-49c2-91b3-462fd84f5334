"""
Progress Tracker for Google Drive KG Orchestrator.

This module provides functionality to track and report progress during
long-running operations like file discovery, categorization, and processing.
"""

import logging
import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class ProgressSnapshot:
    """Snapshot of progress at a specific point in time."""
    timestamp: datetime
    current_step: str
    current_item: int
    total_items: int
    percentage: float
    elapsed_time: float
    estimated_remaining: Optional[float] = None
    items_per_second: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "timestamp": self.timestamp.isoformat(),
            "current_step": self.current_step,
            "current_item": self.current_item,
            "total_items": self.total_items,
            "percentage": self.percentage,
            "elapsed_time": self.elapsed_time,
            "estimated_remaining": self.estimated_remaining,
            "items_per_second": self.items_per_second
        }


class ProgressTracker:
    """
    Tracks progress of long-running operations with detailed metrics.
    
    Features:
    1. Real-time progress tracking with percentages
    2. Time estimation for completion
    3. Performance metrics (items per second)
    4. Progress history and snapshots
    5. Configurable update intervals
    """
    
    def __init__(self, update_interval: float = 1.0):
        """
        Initialize the progress tracker.
        
        Args:
            update_interval: Minimum seconds between progress updates
        """
        self.update_interval = update_interval
        self.start_time: Optional[datetime] = None
        self.last_update_time: Optional[datetime] = None
        
        # Current progress state
        self.current_step: str = ""
        self.current_item: int = 0
        self.total_items: int = 0
        self.is_active: bool = False
        
        # Progress history
        self.snapshots: List[ProgressSnapshot] = []
        self.step_history: List[Dict[str, Any]] = []
        
        # Performance tracking
        self.items_processed_history: List[tuple] = []  # (timestamp, items_processed)
        
        logger.debug("Initialized ProgressTracker")
    
    def start(self, step_name: str, total_items: int = 0):
        """
        Start tracking progress for a new step.
        
        Args:
            step_name: Name of the step being tracked
            total_items: Total number of items to process (0 for unknown)
        """
        self.start_time = datetime.now()
        self.last_update_time = self.start_time
        self.current_step = step_name
        self.current_item = 0
        self.total_items = total_items
        self.is_active = True
        
        # Clear previous data
        self.snapshots.clear()
        self.items_processed_history.clear()
        
        logger.info(f"Started progress tracking: {step_name} ({total_items} items)")
    
    def update_progress(
        self, 
        message: str = "", 
        current_item: Optional[int] = None, 
        total_items: Optional[int] = None,
        force_update: bool = False
    ):
        """
        Update progress with current status.
        
        Args:
            message: Current progress message
            current_item: Current item number (auto-increments if None)
            total_items: Update total items if provided
            force_update: Force update even if interval hasn't passed
        """
        if not self.is_active:
            return
        
        now = datetime.now()
        
        # Check if enough time has passed since last update
        if (not force_update and 
            self.last_update_time and 
            (now - self.last_update_time).total_seconds() < self.update_interval):
            return
        
        # Update counters
        if current_item is not None:
            self.current_item = current_item
        else:
            self.current_item += 1
        
        if total_items is not None:
            self.total_items = total_items
        
        # Calculate metrics
        elapsed_time = (now - self.start_time).total_seconds() if self.start_time else 0
        percentage = (self.current_item / self.total_items * 100) if self.total_items > 0 else 0
        
        # Calculate processing rate
        items_per_second = self.current_item / elapsed_time if elapsed_time > 0 else 0
        
        # Estimate remaining time
        estimated_remaining = None
        if self.total_items > 0 and items_per_second > 0:
            remaining_items = self.total_items - self.current_item
            estimated_remaining = remaining_items / items_per_second
        
        # Create snapshot
        snapshot = ProgressSnapshot(
            timestamp=now,
            current_step=self.current_step,
            current_item=self.current_item,
            total_items=self.total_items,
            percentage=percentage,
            elapsed_time=elapsed_time,
            estimated_remaining=estimated_remaining,
            items_per_second=items_per_second
        )
        
        self.snapshots.append(snapshot)
        self.items_processed_history.append((now, self.current_item))
        
        # Log progress
        if message:
            if self.total_items > 0:
                logger.info(f"{message} ({self.current_item}/{self.total_items}, {percentage:.1f}%)")
            else:
                logger.info(f"{message} ({self.current_item} items)")
        
        self.last_update_time = now
    
    def complete_step(self, message: str = ""):
        """
        Mark current step as completed.
        
        Args:
            message: Completion message
        """
        if not self.is_active:
            return
        
        end_time = datetime.now()
        elapsed_time = (end_time - self.start_time).total_seconds() if self.start_time else 0
        
        # Record step completion
        step_record = {
            "step_name": self.current_step,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": end_time.isoformat(),
            "elapsed_time": elapsed_time,
            "items_processed": self.current_item,
            "total_items": self.total_items,
            "success": True,
            "message": message
        }
        
        self.step_history.append(step_record)
        
        # Final progress update
        self.update_progress(message or f"Completed {self.current_step}", force_update=True)
        
        logger.info(f"Completed step: {self.current_step} in {elapsed_time:.1f}s ({self.current_item} items)")
        
        self.is_active = False
    
    def fail_step(self, error_message: str):
        """
        Mark current step as failed.
        
        Args:
            error_message: Error description
        """
        if not self.is_active:
            return
        
        end_time = datetime.now()
        elapsed_time = (end_time - self.start_time).total_seconds() if self.start_time else 0
        
        # Record step failure
        step_record = {
            "step_name": self.current_step,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": end_time.isoformat(),
            "elapsed_time": elapsed_time,
            "items_processed": self.current_item,
            "total_items": self.total_items,
            "success": False,
            "error_message": error_message
        }
        
        self.step_history.append(step_record)
        
        logger.error(f"Step failed: {self.current_step} after {elapsed_time:.1f}s - {error_message}")
        
        self.is_active = False
    
    def get_current_status(self) -> Dict[str, Any]:
        """
        Get current progress status.
        
        Returns:
            Dictionary with current progress information
        """
        if not self.snapshots:
            return {
                "is_active": self.is_active,
                "current_step": self.current_step,
                "message": "No progress data available"
            }
        
        latest = self.snapshots[-1]
        
        return {
            "is_active": self.is_active,
            "current_step": self.current_step,
            "current_item": self.current_item,
            "total_items": self.total_items,
            "percentage": latest.percentage,
            "elapsed_time": latest.elapsed_time,
            "estimated_remaining": latest.estimated_remaining,
            "items_per_second": latest.items_per_second,
            "last_update": latest.timestamp.isoformat()
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics and statistics.
        
        Returns:
            Dictionary with performance information
        """
        if not self.snapshots:
            return {"error": "No performance data available"}
        
        # Calculate average processing rate
        rates = [s.items_per_second for s in self.snapshots if s.items_per_second > 0]
        avg_rate = sum(rates) / len(rates) if rates else 0
        
        # Calculate rate trend (last 5 vs first 5 snapshots)
        rate_trend = "stable"
        if len(self.snapshots) >= 10:
            early_rates = [s.items_per_second for s in self.snapshots[:5]]
            recent_rates = [s.items_per_second for s in self.snapshots[-5:]]
            
            early_avg = sum(early_rates) / len(early_rates) if early_rates else 0
            recent_avg = sum(recent_rates) / len(recent_rates) if recent_rates else 0
            
            if recent_avg > early_avg * 1.1:
                rate_trend = "improving"
            elif recent_avg < early_avg * 0.9:
                rate_trend = "declining"
        
        return {
            "total_snapshots": len(self.snapshots),
            "average_rate": avg_rate,
            "current_rate": self.snapshots[-1].items_per_second,
            "rate_trend": rate_trend,
            "total_elapsed": self.snapshots[-1].elapsed_time,
            "steps_completed": len(self.step_history)
        }
    
    def get_step_history(self) -> List[Dict[str, Any]]:
        """Get history of completed steps."""
        return self.step_history.copy()
    
    def reset(self):
        """Reset all progress tracking data."""
        self.start_time = None
        self.last_update_time = None
        self.current_step = ""
        self.current_item = 0
        self.total_items = 0
        self.is_active = False
        self.snapshots.clear()
        self.step_history.clear()
        self.items_processed_history.clear()
        
        logger.debug("Progress tracker reset")
    
    def export_progress_data(self) -> Dict[str, Any]:
        """
        Export all progress data for analysis or reporting.
        
        Returns:
            Dictionary with complete progress data
        """
        return {
            "tracker_info": {
                "update_interval": self.update_interval,
                "is_active": self.is_active,
                "current_step": self.current_step
            },
            "current_status": self.get_current_status(),
            "performance_metrics": self.get_performance_metrics(),
            "step_history": self.step_history,
            "snapshots": [s.to_dict() for s in self.snapshots]
        }
