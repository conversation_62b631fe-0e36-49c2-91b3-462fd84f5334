"""
File Categorizer for Google Drive KG Orchestrator.

This module provides functionality to categorize files based on various criteria
including file extensions, content keywords, folder patterns, and content analysis.
"""

import logging
import fnmatch
from typing import List, Dict, Optional

from ..config.settings import CategoryConfig
from ..models.file_info import FileInfo
from ..models.category import Category, CategoryMatch, CategoryResult

logger = logging.getLogger(__name__)


class FileCategorizer:
    """
    Categorizes files based on configured rules and content analysis.
    
    The categorizer uses multiple strategies:
    1. File extension matching
    2. Folder pattern matching  
    3. Content keyword matching
    4. File size constraints
    5. Optional LLM-based content analysis
    """
    
    def __init__(self, categories: Dict[str, CategoryConfig]):
        """
        Initialize the file categorizer.
        
        Args:
            categories: Dictionary of category configurations
        """
        self.categories = categories
        self.enabled_categories = {
            name: config for name, config in categories.items() 
            if config.enabled
        }
        
        # Categorization settings (could be moved to config)
        self.min_confidence_score = 0.6
        self.max_categories_per_file = 2
        self.use_content_analysis = False  # Disabled for now
        self.content_sample_size = 2000
        
        logger.info(f"Initialized FileCategorizer with {len(self.enabled_categories)} enabled categories")
    
    def categorize_file(self, file_info: FileInfo) -> List[Category]:
        """
        Categorize a single file and return matching categories.
        
        Args:
            file_info: File information to categorize
            
        Returns:
            List of Category objects with confidence scores
        """
        try:
            # Create categorization result
            result = CategoryResult(
                file_id=file_info.file_id,
                file_name=file_info.name,
                file_path=file_info.full_path
            )
            
            # Apply rule-based categorization
            matches = self._apply_rule_based_categorization(file_info)
            
            # Filter matches by confidence threshold
            filtered_matches = [
                match for match in matches 
                if match.confidence_score >= self.min_confidence_score
            ]
            
            # Limit number of categories per file
            if len(filtered_matches) > self.max_categories_per_file:
                filtered_matches = sorted(
                    filtered_matches, 
                    key=lambda x: x.confidence_score, 
                    reverse=True
                )[:self.max_categories_per_file]
            
            # Convert matches to categories
            categories = []
            for match in filtered_matches:
                category_config = self.enabled_categories.get(match.category_name)
                description = category_config.description if category_config else ""
                category = match.to_category(description)
                categories.append(category)
                result.add_match(match)
            
            # Apply default category if no matches
            if not categories:
                default_category = self._get_default_category()
                if default_category:
                    categories.append(default_category)
            
            result.success = True
            logger.debug(f"Categorized file {file_info.name}: {[c.name for c in categories]}")
            
            return categories
            
        except Exception as e:
            logger.error(f"Failed to categorize file {file_info.name}: {e}")
            # Return default category on error
            default_category = self._get_default_category()
            return [default_category] if default_category else []
    
    def _apply_rule_based_categorization(self, file_info: FileInfo) -> List[CategoryMatch]:
        """
        Apply rule-based categorization using file attributes.
        
        Args:
            file_info: File information to analyze
            
        Returns:
            List of CategoryMatch objects
        """
        matches = []
        
        for category_name, config in self.enabled_categories.items():
            match = CategoryMatch(
                category_name=category_name,
                confidence_score=0.0,
                file_path=file_info.full_path,
                file_size=file_info.metadata.size
            )
            
            # Check file extension
            extension_score = self._check_file_extension(file_info, config, match)
            
            # Check folder patterns
            folder_score = self._check_folder_patterns(file_info, config, match)
            
            # Check file size constraints
            size_valid = self._check_file_size(file_info, config)
            
            # Check exclude patterns
            excluded = self._check_exclude_patterns(file_info, config)
            
            # Calculate total confidence score
            total_score = extension_score + folder_score
            
            # Apply priority boost
            priority_boost = (10 - config.priority) * 0.05  # Higher priority = lower number
            total_score += priority_boost
            
            # Set final confidence score
            match.confidence_score = min(1.0, total_score)
            
            # Only include if valid and not excluded
            if size_valid and not excluded and match.confidence_score > 0:
                matches.append(match)
        
        return matches
    
    def _check_file_extension(self, file_info: FileInfo, config: CategoryConfig, match: CategoryMatch) -> float:
        """Check if file extension matches category."""
        if not config.file_extensions:
            return 0.0
        
        file_ext = file_info.extension.lower()
        for ext in config.file_extensions:
            if file_ext == ext.lower():
                match.extension_matches.append(ext)
                match.add_match_reason(f"Extension match: {ext}")
                return 0.4  # High confidence for extension match
        
        return 0.0
    
    def _check_folder_patterns(self, file_info: FileInfo, config: CategoryConfig, match: CategoryMatch) -> float:
        """Check if folder path matches category patterns."""
        if not config.folder_patterns:
            return 0.0
        
        folder_path = file_info.drive_info.folder_path.lower()
        score = 0.0
        
        for pattern in config.folder_patterns:
            pattern_lower = pattern.lower()
            if fnmatch.fnmatch(folder_path, pattern_lower):
                match.folder_pattern_matches.append(pattern)
                match.add_match_reason(f"Folder pattern match: {pattern}")
                score += 0.3  # Medium confidence for folder match
        
        return min(0.6, score)  # Cap folder score
    
    def _check_file_size(self, file_info: FileInfo, config: CategoryConfig) -> bool:
        """Check if file size meets category constraints."""
        file_size = file_info.metadata.size
        
        if config.min_file_size and file_size < config.min_file_size:
            return False
        
        if config.max_file_size and file_size > config.max_file_size:
            return False
        
        return True
    
    def _check_exclude_patterns(self, file_info: FileInfo, config: CategoryConfig) -> bool:
        """Check if file matches exclude patterns."""
        if not config.exclude_patterns:
            return False
        
        file_path = file_info.full_path.lower()
        
        for pattern in config.exclude_patterns:
            pattern_lower = pattern.lower()
            if fnmatch.fnmatch(file_path, pattern_lower):
                return True
        
        return False
    
    def _get_default_category(self) -> Optional[Category]:
        """Get default category for uncategorized files."""
        # Look for a category named 'miscellaneous' or 'default'
        for name in ['miscellaneous', 'default', 'other', 'uncategorized']:
            if name in self.enabled_categories:
                config = self.enabled_categories[name]
                return Category(
                    name=name,
                    description=config.description,
                    confidence_score=0.5,
                    priority=config.priority
                )
        
        # Create a basic default category
        return Category(
            name="miscellaneous",
            description="Files that don't match any specific category",
            confidence_score=0.5,
            priority=999
        )
    
    def get_category_stats(self) -> Dict[str, Dict[str, any]]:
        """Get statistics about configured categories."""
        stats = {}
        for name, config in self.enabled_categories.items():
            stats[name] = {
                "enabled": config.enabled,
                "priority": config.priority,
                "file_extensions": len(config.file_extensions),
                "content_keywords": len(config.content_keywords),
                "folder_patterns": len(config.folder_patterns),
                "exclude_patterns": len(config.exclude_patterns),
                "min_file_size": config.min_file_size,
                "max_file_size": config.max_file_size
            }
        return stats
