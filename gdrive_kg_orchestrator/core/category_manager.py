"""
Category Manager for Google Drive KG Orchestrator.

This module provides functionality to manage and organize file categories,
including validation, filtering, and category-specific operations.
"""

import logging
from typing import Dict, List, Optional, Set, Any
from collections import defaultdict, Counter

from ..config.settings import CategoryConfig
from ..models.file_info import FileInfo
from ..models.category import Category

logger = logging.getLogger(__name__)


class CategoryManager:
    """
    Manages file categories and provides category-specific operations.
    
    The manager handles:
    1. Category validation and configuration
    2. File organization by category
    3. Category statistics and reporting
    4. Category-specific processing rules
    """
    
    def __init__(self, categories: Dict[str, CategoryConfig]):
        """
        Initialize the category manager.
        
        Args:
            categories: Dictionary of category configurations
        """
        self.categories = categories
        self.enabled_categories = {
            name: config for name, config in categories.items() 
            if config.enabled
        }
        
        # Category organization
        self.category_hierarchy = self._build_category_hierarchy()
        self.priority_order = self._build_priority_order()
        
        logger.info(f"Initialized CategoryManager with {len(self.enabled_categories)} enabled categories")
    
    def validate_categories(self) -> Dict[str, List[str]]:
        """
        Validate category configurations and return any issues.
        
        Returns:
            Dictionary with category names as keys and lists of validation issues
        """
        issues = {}
        
        for name, config in self.categories.items():
            category_issues = []
            
            # Check required fields
            if not config.name or not config.name.strip():
                category_issues.append("Category name is empty")
            
            if not config.description or not config.description.strip():
                category_issues.append("Category description is empty")
            
            # Check priority range
            if config.priority < 1 or config.priority > 999:
                category_issues.append(f"Priority {config.priority} is out of range (1-999)")
            
            # Check file size constraints
            if config.min_file_size < 0:
                category_issues.append("Minimum file size cannot be negative")
            
            if config.max_file_size and config.max_file_size < config.min_file_size:
                category_issues.append("Maximum file size is less than minimum file size")
            
            # Check for empty criteria
            has_criteria = (
                config.file_extensions or 
                config.content_keywords or 
                config.folder_patterns
            )
            if not has_criteria:
                category_issues.append("Category has no matching criteria defined")
            
            if category_issues:
                issues[name] = category_issues
        
        return issues
    
    def get_category_by_name(self, name: str) -> Optional[CategoryConfig]:
        """Get category configuration by name."""
        return self.categories.get(name)
    
    def get_enabled_categories(self) -> Dict[str, CategoryConfig]:
        """Get all enabled categories."""
        return self.enabled_categories.copy()
    
    def get_categories_by_priority(self) -> List[CategoryConfig]:
        """Get categories ordered by priority (highest first)."""
        return [self.categories[name] for name in self.priority_order]
    
    def organize_files_by_category(self, files: List[FileInfo]) -> Dict[str, List[FileInfo]]:
        """
        Organize files by their assigned categories.
        
        Args:
            files: List of FileInfo objects with categories assigned
            
        Returns:
            Dictionary mapping category names to lists of files
        """
        categorized_files = defaultdict(list)
        
        for file_info in files:
            if file_info.categories:
                # Add file to each of its categories
                for category_name in file_info.categories:
                    if category_name in self.enabled_categories:
                        categorized_files[category_name].append(file_info)
            else:
                # Add to miscellaneous if no categories
                categorized_files["miscellaneous"].append(file_info)
        
        return dict(categorized_files)
    
    def get_category_statistics(self, categorized_files: Dict[str, List[FileInfo]]) -> Dict[str, Dict[str, Any]]:
        """
        Generate statistics for each category.
        
        Args:
            categorized_files: Dictionary of categorized files
            
        Returns:
            Dictionary with category statistics
        """
        stats = {}
        
        for category_name, files in categorized_files.items():
            if not files:
                continue
            
            # Basic file stats
            file_count = len(files)
            total_size = sum(f.metadata.size for f in files)
            avg_size = total_size / file_count if file_count > 0 else 0
            
            # File type distribution
            extensions = Counter(f.extension.lower() for f in files)
            
            # Folder distribution
            folders = Counter(f.drive_info.folder_path for f in files)
            
            # Processing status
            status_counts = Counter(f.processing_status for f in files)
            
            # Size distribution
            size_ranges = {
                "small (<1MB)": 0,
                "medium (1-10MB)": 0,
                "large (10-100MB)": 0,
                "very_large (>100MB)": 0
            }
            
            for file_info in files:
                size_mb = file_info.size_mb
                if size_mb < 1:
                    size_ranges["small (<1MB)"] += 1
                elif size_mb < 10:
                    size_ranges["medium (1-10MB)"] += 1
                elif size_mb < 100:
                    size_ranges["large (10-100MB)"] += 1
                else:
                    size_ranges["very_large (>100MB)"] += 1
            
            # Category configuration
            config = self.categories.get(category_name)
            
            stats[category_name] = {
                "file_count": file_count,
                "total_size_mb": total_size / (1024 * 1024),
                "avg_size_mb": avg_size / (1024 * 1024),
                "file_extensions": dict(extensions.most_common(10)),
                "top_folders": dict(folders.most_common(5)),
                "processing_status": dict(status_counts),
                "size_distribution": size_ranges,
                "priority": config.priority if config else 999,
                "enabled": config.enabled if config else False,
                "has_kg_override": bool(config.kg_config_override) if config else False
            }
        
        return stats
    
    def filter_categories_for_processing(
        self, 
        categorized_files: Dict[str, List[FileInfo]],
        min_files: int = 1,
        max_files: Optional[int] = None,
        categories_to_include: Optional[List[str]] = None,
        categories_to_exclude: Optional[List[str]] = None
    ) -> Dict[str, List[FileInfo]]:
        """
        Filter categories based on various criteria for processing.
        
        Args:
            categorized_files: Dictionary of categorized files
            min_files: Minimum number of files required for processing
            max_files: Maximum number of files to process per category
            categories_to_include: Specific categories to include
            categories_to_exclude: Categories to exclude
            
        Returns:
            Filtered dictionary of categorized files
        """
        filtered = {}
        
        for category_name, files in categorized_files.items():
            # Skip if not enough files
            if len(files) < min_files:
                logger.debug(f"Skipping category {category_name}: only {len(files)} files (min: {min_files})")
                continue
            
            # Skip if category is disabled
            if category_name in self.categories and not self.categories[category_name].enabled:
                logger.debug(f"Skipping disabled category: {category_name}")
                continue
            
            # Apply include filter
            if categories_to_include and category_name not in categories_to_include:
                continue
            
            # Apply exclude filter
            if categories_to_exclude and category_name in categories_to_exclude:
                continue
            
            # Limit number of files if specified
            category_files = files
            if max_files and len(files) > max_files:
                # Sort by file size (smaller files first for faster processing)
                category_files = sorted(files, key=lambda f: f.metadata.size)[:max_files]
                logger.info(f"Limited category {category_name} to {max_files} files (from {len(files)})")
            
            filtered[category_name] = category_files
        
        return filtered
    
    def get_kg_config_for_category(self, category_name: str) -> Dict[str, Any]:
        """
        Get knowledge graph configuration for a specific category.
        
        Args:
            category_name: Name of the category
            
        Returns:
            Dictionary with KG configuration overrides
        """
        config = self.categories.get(category_name)
        if config and config.kg_config_override:
            return config.kg_config_override.copy()
        return {}
    
    def _build_category_hierarchy(self) -> Dict[str, List[str]]:
        """Build category hierarchy based on priorities and relationships."""
        # For now, just group by priority levels
        hierarchy = defaultdict(list)
        
        for name, config in self.categories.items():
            priority_level = f"priority_{config.priority}"
            hierarchy[priority_level].append(name)
        
        return dict(hierarchy)
    
    def _build_priority_order(self) -> List[str]:
        """Build list of category names ordered by priority."""
        return sorted(
            self.categories.keys(),
            key=lambda name: self.categories[name].priority
        )
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary information about the category manager."""
        return {
            "total_categories": len(self.categories),
            "enabled_categories": len(self.enabled_categories),
            "disabled_categories": len(self.categories) - len(self.enabled_categories),
            "priority_range": {
                "min": min(c.priority for c in self.categories.values()) if self.categories else 0,
                "max": max(c.priority for c in self.categories.values()) if self.categories else 0
            },
            "categories_with_kg_overrides": sum(
                1 for c in self.categories.values() if c.kg_config_override
            ),
            "validation_issues": len(self.validate_categories())
        }
