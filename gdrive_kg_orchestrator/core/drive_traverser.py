"""
Google Drive traversal module for discovering and listing files.

This module provides functionality to recursively traverse Google Drive folders,
discover files, extract metadata, and organize the file structure for processing.
"""

import logging
import time
from typing import Dict, List, Optional, Iterator, Set
from datetime import datetime
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import async<PERSON>
from concurrent.futures import ThreadPoolExecutor, as_completed

from ..models.file_info import FileInfo, DriveFileInfo, FileMetadata
from ..config.settings import DriveConfig
from ..utils.drive_auth import DriveAuthenticator
from ..utils.progress_tracker import ProgressTracker

logger = logging.getLogger(__name__)


class DriveTraverser:
    """
    Google Drive traversal engine for discovering files and folders.
    
    This class provides methods to:
    - Recursively traverse folder structures
    - Extract file metadata and information
    - Handle rate limiting and error recovery
    - Track progress and provide status updates
    """
    
    def __init__(self, config: DriveConfig, authenticator: DriveAuthenticator):
        """
        Initialize the Drive traverser.
        
        Args:
            config: Drive configuration settings
            authenticator: Google Drive authentication handler
        """
        self.config = config
        self.authenticator = authenticator
        self.service = None
        self.progress_tracker = ProgressTracker()
        
        # Rate limiting
        self.last_request_time = 0
        self.request_interval = 1.0 / config.rate_limit_requests_per_second
        
        # Caching and state
        self.folder_cache: Dict[str, Dict] = {}
        self.discovered_files: Dict[str, FileInfo] = {}
        self.folder_structure: Dict[str, List[str]] = {}
        
        # Statistics
        self.stats = {
            'folders_traversed': 0,
            'files_discovered': 0,
            'errors_encountered': 0,
            'api_calls_made': 0,
            'total_size_bytes': 0
        }
    
    def initialize(self):
        """Initialize the Google Drive service."""
        try:
            credentials = self.authenticator.get_credentials()
            self.service = build('drive', 'v3', credentials=credentials)
            logger.info("Google Drive service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Google Drive service: {e}")
            raise
    
    def _rate_limit(self):
        """Apply rate limiting to API requests."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.request_interval:
            sleep_time = self.request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_api_call(self, request_func, *args, **kwargs):
        """Make a rate-limited API call with error handling."""
        self._rate_limit()
        self.stats['api_calls_made'] += 1
        
        try:
            return request_func(*args, **kwargs)
        except HttpError as e:
            self.stats['errors_encountered'] += 1
            logger.error(f"Google Drive API error: {e}")
            
            # Handle specific error codes
            if e.resp.status == 429:  # Rate limit exceeded
                logger.warning("Rate limit exceeded, waiting before retry...")
                time.sleep(5)
                return self._make_api_call(request_func, *args, **kwargs)
            elif e.resp.status == 403:  # Forbidden
                logger.error("Access forbidden - check permissions")
                raise
            elif e.resp.status == 404:  # Not found
                logger.warning("Resource not found")
                return None
            else:
                raise
    
    def get_folder_info(self, folder_id: str) -> Optional[Dict]:
        """Get information about a specific folder."""
        if folder_id in self.folder_cache:
            return self.folder_cache[folder_id]
        
        try:
            result = self._make_api_call(
                self.service.files().get,
                fileId=folder_id,
                fields='id,name,parents,mimeType,createdTime,modifiedTime,size,webViewLink'
            ).execute()
            
            self.folder_cache[folder_id] = result
            return result
        except Exception as e:
            logger.error(f"Failed to get folder info for {folder_id}: {e}")
            return None
    
    def list_folder_contents(self, folder_id: str, page_token: Optional[str] = None) -> Dict:
        """List contents of a specific folder."""
        query = f"'{folder_id}' in parents and trashed=false"
        
        # Apply file filters if configured
        if self.config.included_extensions:
            ext_conditions = []
            for ext in self.config.included_extensions:
                ext_conditions.append(f"name contains '{ext}'")
            query += f" and ({' or '.join(ext_conditions)})"
        
        if self.config.excluded_extensions:
            for ext in self.config.excluded_extensions:
                query += f" and not name contains '{ext}'"
        
        try:
            request = self.service.files().list(
                q=query,
                pageSize=min(self.config.batch_size, 1000),
                pageToken=page_token,
                fields='nextPageToken,files(id,name,parents,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink,shared,ownedByMe)'
            )
            
            return self._make_api_call(request.execute)
        except Exception as e:
            logger.error(f"Failed to list folder contents for {folder_id}: {e}")
            return {'files': [], 'nextPageToken': None}
    
    def traverse_folder(self, folder_id: str, folder_path: str = "", depth: int = 0) -> Iterator[FileInfo]:
        """
        Recursively traverse a folder and yield file information.
        
        Args:
            folder_id: Google Drive folder ID to traverse
            folder_path: Current folder path for context
            depth: Current traversal depth
            
        Yields:
            FileInfo objects for discovered files
        """
        if depth > self.config.max_depth:
            logger.warning(f"Maximum depth {self.config.max_depth} reached at {folder_path}")
            return
        
        logger.info(f"Traversing folder: {folder_path} (depth: {depth})")
        self.stats['folders_traversed'] += 1
        
        # Get folder information
        folder_info = self.get_folder_info(folder_id)
        if not folder_info:
            logger.error(f"Could not access folder {folder_id}")
            return
        
        folder_name = folder_info.get('name', 'Unknown')
        current_path = f"{folder_path}/{folder_name}" if folder_path else folder_name
        
        # Track folder structure
        if folder_id not in self.folder_structure:
            self.folder_structure[folder_id] = []
        
        # List folder contents with pagination
        page_token = None
        files_in_folder = 0
        
        while True:
            try:
                result = self.list_folder_contents(folder_id, page_token)
                files = result.get('files', [])
                
                if not files:
                    break
                
                # Process files and folders
                folders_to_traverse = []
                
                for file_data in files:
                    files_in_folder += 1
                    
                    # Check file count limit
                    if files_in_folder > self.config.max_files_per_folder:
                        logger.warning(f"File limit reached in folder {current_path}")
                        break
                    
                    # Create FileInfo object
                    file_info = FileInfo.from_drive_file(
                        file_data, 
                        folder_path=current_path,
                        folder_depth=depth
                    )
                    
                    # Track in folder structure
                    self.folder_structure[folder_id].append(file_info.file_id)
                    
                    # Apply file size filter
                    if file_info.metadata.size_mb > self.config.file_size_limit_mb:
                        logger.debug(f"Skipping large file: {file_info.name} ({file_info.size_mb:.1f} MB)")
                        continue
                    
                    if file_info.drive_info.is_folder:
                        # Queue folder for traversal
                        folders_to_traverse.append((file_info.file_id, current_path, depth + 1))
                    else:
                        # Yield file for processing
                        self.stats['files_discovered'] += 1
                        self.stats['total_size_bytes'] += file_info.metadata.size
                        self.discovered_files[file_info.file_id] = file_info
                        
                        # Update progress
                        self.progress_tracker.update_progress(
                            f"Discovered: {file_info.name}",
                            self.stats['files_discovered']
                        )
                        
                        yield file_info
                
                # Check for next page
                page_token = result.get('nextPageToken')
                if not page_token:
                    break
                    
            except Exception as e:
                logger.error(f"Error processing folder {current_path}: {e}")
                break
        
        # Recursively traverse subfolders
        for subfolder_id, subfolder_path, subfolder_depth in folders_to_traverse:
            try:
                yield from self.traverse_folder(subfolder_id, subfolder_path, subfolder_depth)
            except Exception as e:
                logger.error(f"Error traversing subfolder {subfolder_id}: {e}")
                continue
    
    def discover_all_files(self, root_folder_id: Optional[str] = None) -> List[FileInfo]:
        """
        Discover all files in the specified root folder or entire Drive.
        
        Args:
            root_folder_id: Root folder ID to start traversal from
            
        Returns:
            List of discovered FileInfo objects
        """
        if not self.service:
            self.initialize()
        
        start_time = datetime.now()
        logger.info(f"Starting file discovery from folder: {root_folder_id or 'root'}")
        
        # Reset statistics
        self.stats = {key: 0 for key in self.stats}
        self.discovered_files.clear()
        
        # Start traversal
        discovered_files = []
        
        try:
            if root_folder_id:
                # Traverse specific folder
                for file_info in self.traverse_folder(root_folder_id):
                    discovered_files.append(file_info)
            else:
                # Traverse entire Drive (use 'root' folder)
                for file_info in self.traverse_folder('root'):
                    discovered_files.append(file_info)
        
        except Exception as e:
            logger.error(f"Error during file discovery: {e}")
            raise
        
        # Log completion statistics
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"File discovery completed in {duration:.1f} seconds")
        logger.info(f"Statistics: {self.stats}")
        logger.info(f"Discovered {len(discovered_files)} files")
        
        return discovered_files
    
    def get_file_content_preview(self, file_info: FileInfo, max_chars: int = 2000) -> Optional[str]:
        """
        Get a preview of file content for categorization.
        
        Args:
            file_info: File information object
            max_chars: Maximum characters to retrieve
            
        Returns:
            File content preview or None if not accessible
        """
        if not file_info.metadata.is_text_file and not file_info.metadata.is_document:
            return None
        
        try:
            # For text files, get content directly
            if file_info.metadata.is_text_file:
                request = self.service.files().get_media(fileId=file_info.file_id)
                content = self._make_api_call(request.execute)
                
                if isinstance(content, bytes):
                    content = content.decode('utf-8', errors='ignore')
                
                return content[:max_chars] if content else None
            
            # For other document types, would need additional processing
            # This is a placeholder for document content extraction
            logger.debug(f"Content extraction not implemented for {file_info.metadata.mime_type}")
            return None
            
        except Exception as e:
            logger.error(f"Failed to get content preview for {file_info.name}: {e}")
            return None
    
    def get_statistics(self) -> Dict:
        """Get traversal statistics."""
        return {
            **self.stats,
            'total_size_mb': self.stats['total_size_bytes'] / (1024 * 1024),
            'avg_file_size_mb': (
                self.stats['total_size_bytes'] / (1024 * 1024) / self.stats['files_discovered']
                if self.stats['files_discovered'] > 0 else 0
            ),
            'folders_in_cache': len(self.folder_cache),
            'folder_structure_entries': len(self.folder_structure)
        }
