"""
Knowledge Graph Orchestrator for Google Drive KG Orchestrator.

This module coordinates knowledge graph creation using the enterprise_kg_minimal system.
It processes files and creates knowledge graphs with proper configuration management.
"""

import logging
import os
import tempfile
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..config.settings import KGConfig
from ..models.file_info import FileInfo
from ..models.processing_result import KGProcessingResult

logger = logging.getLogger(__name__)


class KGOrchestrator:
    """
    Orchestrates knowledge graph creation for categorized files.
    
    This class integrates with the enterprise_kg_minimal system to:
    1. Process files and create chunks
    2. Extract entities and relationships
    3. Store results in Neo4j and vector databases
    4. Track processing statistics and errors
    """
    
    def __init__(self, kg_config: KGConfig):
        """
        Initialize the KG orchestrator.
        
        Args:
            kg_config: Knowledge graph configuration
        """
        self.config = kg_config
        self.enterprise_kg = None
        
        # Initialize enterprise KG system
        self._initialize_enterprise_kg()
        
        logger.info(f"Initialized KGOrchestrator with provider: {kg_config.llm_provider}")
    
    def _initialize_enterprise_kg(self):
        """Initialize the enterprise KG minimal system."""
        try:
            # Import enterprise_kg_minimal
            from enterprise_kg_minimal import process_document
            self.process_document_func = process_document
            
            # Set up configuration for enterprise_kg_minimal
            self._setup_enterprise_kg_config()
            
            logger.info("Successfully initialized enterprise_kg_minimal integration")
            
        except ImportError as e:
            logger.error(f"Failed to import enterprise_kg_minimal: {e}")
            logger.error("Make sure enterprise_kg_minimal is installed and available")
            self.process_document_func = None
        except Exception as e:
            logger.error(f"Failed to initialize enterprise_kg_minimal: {e}")
            self.process_document_func = None
    
    def _setup_enterprise_kg_config(self):
        """Setup configuration for enterprise_kg_minimal."""
        # Set environment variables for enterprise_kg_minimal
        os.environ["NEO4J_URI"] = self.config.neo4j_uri
        os.environ["NEO4J_USER"] = self.config.neo4j_user
        os.environ["NEO4J_PASSWORD"] = self.config.neo4j_password
        
        if self.config.neo4j_database:
            os.environ["NEO4J_DATABASE"] = self.config.neo4j_database
        
        # Set LLM configuration
        if self.config.llm_provider.lower() == "openai":
            if self.config.llm_api_key:
                os.environ["OPENAI_API_KEY"] = self.config.llm_api_key
            os.environ["LLM_PROVIDER"] = "openai"
            os.environ["LLM_MODEL"] = self.config.llm_model
        elif self.config.llm_provider.lower() == "anthropic":
            if self.config.llm_api_key:
                os.environ["ANTHROPIC_API_KEY"] = self.config.llm_api_key
            os.environ["LLM_PROVIDER"] = "anthropic"
            os.environ["LLM_MODEL"] = self.config.llm_model
        
        # Set chunking configuration
        os.environ["CHUNKING_STRATEGY"] = self.config.chunking_strategy
        os.environ["CHUNK_SIZE"] = str(self.config.chunk_size)
        os.environ["CHUNK_OVERLAP"] = str(self.config.chunk_overlap)
    
    def process_files(
        self, 
        files: List[FileInfo], 
        category_name: str = "default",
        kg_config_override: Optional[Dict[str, Any]] = None
    ) -> KGProcessingResult:
        """
        Process a list of files and create knowledge graphs.
        
        Args:
            files: List of FileInfo objects to process
            category_name: Name of the category being processed
            kg_config_override: Category-specific configuration overrides
            
        Returns:
            KGProcessingResult with processing statistics
        """
        result = KGProcessingResult()
        result.start_time = datetime.now()
        
        if not self.process_document_func:
            result.mark_completed(False, "Enterprise KG system not available")
            return result
        
        if not files:
            result.mark_completed(True, "No files to process")
            return result
        
        logger.info(f"Processing {len(files)} files for category: {category_name}")
        
        # Apply configuration overrides
        original_config = self._apply_config_overrides(kg_config_override)
        
        try:
            # Process each file
            for file_info in files:
                try:
                    file_result = self._process_single_file(file_info, category_name)
                    
                    if file_result["success"]:
                        result.files_processed += 1
                        result.processed_files.append(file_info.file_id)
                        
                        # Accumulate statistics
                        result.total_chunks += file_result.get("chunks_created", 0)
                        result.total_entities += file_result.get("entities_extracted", 0)
                        result.total_relationships += file_result.get("relationships_created", 0)
                        result.neo4j_nodes_created += file_result.get("neo4j_nodes_created", 0)
                        result.neo4j_relationships_created += file_result.get("neo4j_relationships_created", 0)
                        result.vector_embeddings_created += file_result.get("vector_embeddings_created", 0)
                        
                        # Update file status
                        file_info.update_processing_status("completed")
                        
                    else:
                        result.files_failed += 1
                        result.failed_files.append(file_info.file_id)
                        result.add_warning(f"Failed to process {file_info.name}: {file_result.get('error', 'Unknown error')}")
                        
                        # Update file status
                        file_info.update_processing_status("failed", file_result.get("error"))
                
                except Exception as e:
                    logger.error(f"Error processing file {file_info.name}: {e}")
                    result.files_failed += 1
                    result.failed_files.append(file_info.file_id)
                    result.add_warning(f"Exception processing {file_info.name}: {str(e)}")
                    file_info.update_processing_status("failed", str(e))
            
            # Set processing metadata
            result.chunking_strategy = self.config.chunking_strategy
            result.llm_provider = self.config.llm_provider
            result.llm_model = self.config.llm_model
            
            result.mark_completed(True)
            logger.info(f"Completed processing category {category_name}: {result.files_processed} successful, {result.files_failed} failed")
            
        except Exception as e:
            logger.error(f"Failed to process files for category {category_name}: {e}")
            result.mark_completed(False, str(e))
        
        finally:
            # Restore original configuration
            self._restore_config(original_config)
        
        return result
    
    def _process_single_file(self, file_info: FileInfo, category_name: str) -> Dict[str, Any]:
        """
        Process a single file using enterprise_kg_minimal.
        
        Args:
            file_info: File information
            category_name: Category name for context
            
        Returns:
            Dictionary with processing results
        """
        try:
            # Get file content (this would need to be implemented to actually read from Google Drive)
            file_content = self._get_file_content(file_info)
            
            if not file_content:
                return {
                    "success": False,
                    "error": "Could not retrieve file content"
                }
            
            # Process document using enterprise_kg_minimal
            result = self.process_document_func(
                file_id=file_info.file_id,
                file_content=file_content
            )
            
            if result.get("success", False):
                return {
                    "success": True,
                    "chunks_created": result.get("chunks_created", 0),
                    "entities_extracted": result.get("entities_extracted", 0),
                    "relationships_created": result.get("relationships_created", 0),
                    "neo4j_nodes_created": result.get("neo4j_nodes_created", 0),
                    "neo4j_relationships_created": result.get("neo4j_relationships_created", 0),
                    "vector_embeddings_created": result.get("vector_embeddings_created", 0)
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Processing failed")
                }
        
        except Exception as e:
            logger.error(f"Exception in _process_single_file for {file_info.name}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _get_file_content(self, file_info: FileInfo) -> Optional[str]:
        """
        Get file content for processing.
        
        This is a placeholder - in a real implementation, this would:
        1. Use Google Drive API to download file content
        2. Handle different file types (PDF, DOCX, etc.)
        3. Extract text content appropriately
        
        Args:
            file_info: File information
            
        Returns:
            File content as string or None if unavailable
        """
        # For now, return the content preview if available
        if file_info.content_preview:
            return file_info.content_preview
        
        # In a real implementation, you would:
        # 1. Use the Google Drive API to download the file
        # 2. Extract text based on file type
        # 3. Handle various document formats
        
        logger.warning(f"No content available for file {file_info.name}")
        return None
    
    def _apply_config_overrides(self, overrides: Optional[Dict[str, Any]]) -> Dict[str, str]:
        """Apply configuration overrides and return original values."""
        original_config = {}
        
        if not overrides:
            return original_config
        
        # Store original values and apply overrides
        for key, value in overrides.items():
            env_key = key.upper()
            original_config[env_key] = os.environ.get(env_key)
            os.environ[env_key] = str(value)
        
        return original_config
    
    def _restore_config(self, original_config: Dict[str, str]):
        """Restore original configuration values."""
        for key, value in original_config.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Test connection to Neo4j and other dependencies.
        
        Returns:
            Dictionary with connection test results
        """
        results = {
            "neo4j_connection": False,
            "llm_provider": False,
            "enterprise_kg_available": False,
            "errors": []
        }
        
        # Test enterprise_kg_minimal availability
        if self.process_document_func:
            results["enterprise_kg_available"] = True
        else:
            results["errors"].append("enterprise_kg_minimal not available")
        
        # Test Neo4j connection (would need actual implementation)
        try:
            # This would test actual Neo4j connection
            results["neo4j_connection"] = True
        except Exception as e:
            results["errors"].append(f"Neo4j connection failed: {e}")
        
        # Test LLM provider (would need actual implementation)
        try:
            # This would test LLM API connection
            results["llm_provider"] = True
        except Exception as e:
            results["errors"].append(f"LLM provider test failed: {e}")
        
        return results
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics and system status."""
        return {
            "config": {
                "neo4j_uri": self.config.neo4j_uri,
                "llm_provider": self.config.llm_provider,
                "llm_model": self.config.llm_model,
                "chunking_strategy": self.config.chunking_strategy,
                "chunk_size": self.config.chunk_size,
                "parallel_processing": self.config.parallel_processing,
                "max_workers": self.config.max_workers
            },
            "system_status": self.test_connection()
        }
