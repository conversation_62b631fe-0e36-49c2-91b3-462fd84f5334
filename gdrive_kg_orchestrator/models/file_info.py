"""
File information models for Google Drive KG Orchestrator.

This module defines data structures for representing file metadata,
Google Drive file information, and file processing details.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path


@dataclass
class FileMetadata:
    """Basic file metadata information."""
    name: str
    size: int
    extension: str
    mime_type: Optional[str] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    accessed_at: Optional[datetime] = None
    
    @property
    def size_mb(self) -> float:
        """File size in megabytes."""
        return self.size / (1024 * 1024)
    
    @property
    def is_text_file(self) -> bool:
        """Check if file is a text-based file."""
        text_extensions = {'.txt', '.md', '.rst', '.csv', '.json', '.xml', '.yaml', '.yml'}
        return self.extension.lower() in text_extensions
    
    @property
    def is_document(self) -> bool:
        """Check if file is a document file."""
        doc_extensions = {'.pdf', '.doc', '.docx', '.odt', '.rtf'}
        return self.extension.lower() in doc_extensions
    
    @property
    def is_spreadsheet(self) -> bool:
        """Check if file is a spreadsheet file."""
        sheet_extensions = {'.xls', '.xlsx', '.ods', '.csv', '.tsv'}
        return self.extension.lower() in sheet_extensions
    
    @property
    def is_presentation(self) -> bool:
        """Check if file is a presentation file."""
        pres_extensions = {'.ppt', '.pptx', '.odp'}
        return self.extension.lower() in pres_extensions


@dataclass
class DriveFileInfo:
    """Google Drive specific file information."""
    file_id: str
    name: str
    parents: List[str] = field(default_factory=list)
    web_view_link: Optional[str] = None
    web_content_link: Optional[str] = None
    drive_type: str = "file"  # "file" or "folder"
    shared: bool = False
    owned_by_me: bool = True
    permissions: List[Dict[str, Any]] = field(default_factory=list)
    last_modifying_user: Optional[Dict[str, str]] = None
    sharing_user: Optional[Dict[str, str]] = None
    folder_path: str = ""
    folder_depth: int = 0
    
    @property
    def is_folder(self) -> bool:
        """Check if this is a folder."""
        return self.drive_type == "folder"
    
    @property
    def is_file(self) -> bool:
        """Check if this is a file."""
        return self.drive_type == "file"
    
    @property
    def parent_folder_id(self) -> Optional[str]:
        """Get the primary parent folder ID."""
        return self.parents[0] if self.parents else None


@dataclass
class FileInfo:
    """Complete file information combining metadata and Drive info."""
    drive_info: DriveFileInfo
    metadata: FileMetadata
    content_preview: Optional[str] = None
    content_summary: Optional[str] = None
    processing_status: str = "pending"  # pending, processing, completed, failed
    error_message: Optional[str] = None
    categories: List[str] = field(default_factory=list)
    category_scores: Dict[str, float] = field(default_factory=dict)
    
    # Content analysis results
    word_count: Optional[int] = None
    language: Optional[str] = None
    readability_score: Optional[float] = None
    key_topics: List[str] = field(default_factory=list)
    
    # Processing timestamps
    discovered_at: Optional[datetime] = None
    categorized_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None
    
    @property
    def file_id(self) -> str:
        """Get the Google Drive file ID."""
        return self.drive_info.file_id
    
    @property
    def name(self) -> str:
        """Get the file name."""
        return self.drive_info.name
    
    @property
    def full_path(self) -> str:
        """Get the full path including folder structure."""
        if self.drive_info.folder_path:
            return f"{self.drive_info.folder_path}/{self.name}"
        return self.name
    
    @property
    def size_mb(self) -> float:
        """Get file size in MB."""
        return self.metadata.size_mb
    
    @property
    def extension(self) -> str:
        """Get file extension."""
        return self.metadata.extension
    
    @property
    def is_processable(self) -> bool:
        """Check if file can be processed for knowledge graph creation."""
        # Check file type
        if not (self.metadata.is_text_file or self.metadata.is_document):
            return False
        
        # Check file size (not too small or too large)
        if self.metadata.size < 100:  # Less than 100 bytes
            return False
        if self.metadata.size_mb > 50:  # Larger than 50 MB
            return False
        
        # Check if file has content
        if self.content_preview and len(self.content_preview.strip()) < 50:
            return False
        
        return True
    
    @property
    def primary_category(self) -> Optional[str]:
        """Get the primary (highest scoring) category."""
        if not self.categories:
            return None
        
        if self.category_scores:
            return max(self.category_scores.items(), key=lambda x: x[1])[0]
        
        return self.categories[0]
    
    def add_category(self, category: str, score: float = 1.0):
        """Add a category with optional score."""
        if category not in self.categories:
            self.categories.append(category)
        self.category_scores[category] = score
    
    def remove_category(self, category: str):
        """Remove a category."""
        if category in self.categories:
            self.categories.remove(category)
        if category in self.category_scores:
            del self.category_scores[category]
    
    def update_processing_status(self, status: str, error_message: Optional[str] = None):
        """Update processing status."""
        self.processing_status = status
        self.error_message = error_message
        
        if status == "completed":
            self.processed_at = datetime.now()
        elif status == "categorized":
            self.categorized_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "file_id": self.file_id,
            "name": self.name,
            "full_path": self.full_path,
            "size_mb": self.size_mb,
            "extension": self.extension,
            "mime_type": self.metadata.mime_type,
            "created_at": self.metadata.created_at.isoformat() if self.metadata.created_at else None,
            "modified_at": self.metadata.modified_at.isoformat() if self.metadata.modified_at else None,
            "folder_path": self.drive_info.folder_path,
            "folder_depth": self.drive_info.folder_depth,
            "categories": self.categories,
            "category_scores": self.category_scores,
            "primary_category": self.primary_category,
            "processing_status": self.processing_status,
            "error_message": self.error_message,
            "word_count": self.word_count,
            "language": self.language,
            "key_topics": self.key_topics,
            "is_processable": self.is_processable,
            "discovered_at": self.discovered_at.isoformat() if self.discovered_at else None,
            "categorized_at": self.categorized_at.isoformat() if self.categorized_at else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None
        }
    
    @classmethod
    def from_drive_file(cls, drive_file: Dict[str, Any], folder_path: str = "", folder_depth: int = 0) -> 'FileInfo':
        """Create FileInfo from Google Drive API response."""
        # Extract basic file information
        file_id = drive_file['id']
        name = drive_file['name']
        size = int(drive_file.get('size', 0))
        mime_type = drive_file.get('mimeType')
        
        # Extract timestamps
        created_time = None
        modified_time = None
        if 'createdTime' in drive_file:
            created_time = datetime.fromisoformat(drive_file['createdTime'].replace('Z', '+00:00'))
        if 'modifiedTime' in drive_file:
            modified_time = datetime.fromisoformat(drive_file['modifiedTime'].replace('Z', '+00:00'))
        
        # Determine file extension
        extension = Path(name).suffix if '.' in name else ''
        
        # Create metadata
        metadata = FileMetadata(
            name=name,
            size=size,
            extension=extension,
            mime_type=mime_type,
            created_at=created_time,
            modified_at=modified_time
        )
        
        # Create Drive info
        drive_info = DriveFileInfo(
            file_id=file_id,
            name=name,
            parents=drive_file.get('parents', []),
            web_view_link=drive_file.get('webViewLink'),
            web_content_link=drive_file.get('webContentLink'),
            drive_type="folder" if mime_type == 'application/vnd.google-apps.folder' else "file",
            shared=drive_file.get('shared', False),
            owned_by_me=drive_file.get('ownedByMe', True),
            folder_path=folder_path,
            folder_depth=folder_depth
        )
        
        return cls(
            drive_info=drive_info,
            metadata=metadata,
            discovered_at=datetime.now()
        )
