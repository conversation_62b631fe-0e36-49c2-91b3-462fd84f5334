"""
Category models for Google Drive KG Orchestrator.

This module defines data structures for representing file categories,
category matches, and categorization results.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime


@dataclass
class Category:
    """
    Represents a file category with metadata and confidence scoring.
    """
    name: str
    description: str
    confidence_score: float = 1.0
    
    # Matching criteria that triggered this category
    matched_extensions: List[str] = field(default_factory=list)
    matched_keywords: List[str] = field(default_factory=list)
    matched_folder_patterns: List[str] = field(default_factory=list)
    
    # Category configuration
    priority: int = 1
    enabled: bool = True
    
    # Metadata
    assigned_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Validate category after initialization."""
        if not self.name or not self.name.strip():
            raise ValueError("Category name cannot be empty")
        if not self.description or not self.description.strip():
            raise ValueError("Category description cannot be empty")
        if not 0.0 <= self.confidence_score <= 1.0:
            raise ValueError("Confidence score must be between 0.0 and 1.0")
        
        # Set assignment timestamp
        if self.assigned_at is None:
            self.assigned_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "name": self.name,
            "description": self.description,
            "confidence_score": self.confidence_score,
            "matched_extensions": self.matched_extensions,
            "matched_keywords": self.matched_keywords,
            "matched_folder_patterns": self.matched_folder_patterns,
            "priority": self.priority,
            "enabled": self.enabled,
            "assigned_at": self.assigned_at.isoformat() if self.assigned_at else None
        }


@dataclass
class CategoryMatch:
    """
    Represents a potential category match for a file with detailed matching information.
    """
    category_name: str
    confidence_score: float
    match_reasons: List[str] = field(default_factory=list)
    
    # Specific match details
    extension_matches: List[str] = field(default_factory=list)
    keyword_matches: List[str] = field(default_factory=list)
    folder_pattern_matches: List[str] = field(default_factory=list)
    content_analysis_score: Optional[float] = None
    
    # File context
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    
    def __post_init__(self):
        """Validate match after initialization."""
        if not self.category_name or not self.category_name.strip():
            raise ValueError("Category name cannot be empty")
        if not 0.0 <= self.confidence_score <= 1.0:
            raise ValueError("Confidence score must be between 0.0 and 1.0")
    
    def add_match_reason(self, reason: str, score_boost: float = 0.0):
        """Add a match reason and optionally boost confidence score."""
        if reason not in self.match_reasons:
            self.match_reasons.append(reason)
        
        # Boost confidence score but cap at 1.0
        self.confidence_score = min(1.0, self.confidence_score + score_boost)
    
    def to_category(self, description: str = "") -> Category:
        """Convert match to Category object."""
        return Category(
            name=self.category_name,
            description=description,
            confidence_score=self.confidence_score,
            matched_extensions=self.extension_matches,
            matched_keywords=self.keyword_matches,
            matched_folder_patterns=self.folder_pattern_matches
        )


@dataclass
class CategoryResult:
    """
    Complete categorization result for a file including all potential matches.
    """
    file_id: str
    file_name: str
    file_path: str
    
    # Primary categorization
    primary_category: Optional[Category] = None
    all_matches: List[CategoryMatch] = field(default_factory=list)
    
    # Processing metadata
    processing_time_ms: float = 0.0
    content_analyzed: bool = False
    analysis_method: str = "rule_based"  # rule_based, content_analysis, hybrid
    
    # Error handling
    success: bool = True
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    
    # Timestamps
    processed_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Set processing timestamp."""
        if self.processed_at is None:
            self.processed_at = datetime.now()
    
    @property
    def categories(self) -> List[Category]:
        """Get all categories from matches."""
        categories = []
        for match in self.all_matches:
            category = match.to_category()
            categories.append(category)
        return categories
    
    @property
    def category_names(self) -> List[str]:
        """Get list of category names."""
        return [match.category_name for match in self.all_matches]
    
    @property
    def has_categories(self) -> bool:
        """Check if file has any categories."""
        return len(self.all_matches) > 0
    
    def add_match(self, match: CategoryMatch):
        """Add a category match."""
        self.all_matches.append(match)
        
        # Update primary category if this match has higher confidence
        if (self.primary_category is None or 
            match.confidence_score > self.primary_category.confidence_score):
            self.primary_category = match.to_category()
    
    def get_top_matches(self, limit: int = 3) -> List[CategoryMatch]:
        """Get top N matches by confidence score."""
        sorted_matches = sorted(self.all_matches, 
                              key=lambda x: x.confidence_score, 
                              reverse=True)
        return sorted_matches[:limit]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "file_id": self.file_id,
            "file_name": self.file_name,
            "file_path": self.file_path,
            "primary_category": self.primary_category.to_dict() if self.primary_category else None,
            "all_matches": [match.__dict__ for match in self.all_matches],
            "processing_time_ms": self.processing_time_ms,
            "content_analyzed": self.content_analyzed,
            "analysis_method": self.analysis_method,
            "success": self.success,
            "error_message": self.error_message,
            "warnings": self.warnings,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None
        }
