"""
Processing result models for Google Drive KG Orchestrator.

This module defines data structures for representing processing results
from various stages of the orchestration pipeline.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime


@dataclass
class ProcessingResult:
    """
    Base class for processing results with common metadata.
    """
    success: bool = True
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    processing_time_seconds: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    def __post_init__(self):
        """Set timestamps if not provided."""
        if self.start_time is None:
            self.start_time = datetime.now()
        if self.end_time is None and not self.success:
            self.end_time = datetime.now()
    
    def mark_completed(self, success: bool = True, error_message: Optional[str] = None):
        """Mark processing as completed."""
        self.success = success
        self.error_message = error_message
        self.end_time = datetime.now()
        
        if self.start_time:
            self.processing_time_seconds = (self.end_time - self.start_time).total_seconds()
    
    def add_warning(self, warning: str):
        """Add a warning message."""
        if warning not in self.warnings:
            self.warnings.append(warning)


@dataclass
class KGProcessingResult(ProcessingResult):
    """
    Result from knowledge graph processing for a single file or batch.
    """
    # File processing stats
    files_processed: int = 0
    files_failed: int = 0
    
    # Content processing stats
    total_chunks: int = 0
    total_entities: int = 0
    total_relationships: int = 0
    
    # Storage stats
    neo4j_nodes_created: int = 0
    neo4j_relationships_created: int = 0
    vector_embeddings_created: int = 0
    
    # Processing details
    chunking_strategy: str = "hybrid"
    llm_provider: str = "openai"
    llm_model: str = "gpt-4o"
    
    # File details
    processed_files: List[str] = field(default_factory=list)
    failed_files: List[str] = field(default_factory=list)
    
    @property
    def total_files(self) -> int:
        """Total number of files attempted."""
        return self.files_processed + self.files_failed
    
    @property
    def success_rate(self) -> float:
        """Success rate as percentage."""
        if self.total_files == 0:
            return 0.0
        return (self.files_processed / self.total_files) * 100.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "success": self.success,
            "error_message": self.error_message,
            "warnings": self.warnings,
            "processing_time_seconds": self.processing_time_seconds,
            "files_processed": self.files_processed,
            "files_failed": self.files_failed,
            "total_files": self.total_files,
            "success_rate": self.success_rate,
            "total_chunks": self.total_chunks,
            "total_entities": self.total_entities,
            "total_relationships": self.total_relationships,
            "neo4j_nodes_created": self.neo4j_nodes_created,
            "neo4j_relationships_created": self.neo4j_relationships_created,
            "vector_embeddings_created": self.vector_embeddings_created,
            "chunking_strategy": self.chunking_strategy,
            "llm_provider": self.llm_provider,
            "llm_model": self.llm_model,
            "processed_files": self.processed_files,
            "failed_files": self.failed_files,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None
        }


@dataclass
class CategoryProcessingResult(ProcessingResult):
    """
    Result from processing a specific category of files.
    """
    category_name: str = ""
    
    # File stats
    total_files: int = 0
    files_processed: int = 0
    files_skipped: int = 0
    files_failed: int = 0
    
    # Knowledge graph results
    kg_result: Optional[KGProcessingResult] = None
    kg_created: bool = False
    
    # Category-specific stats
    avg_file_size_mb: float = 0.0
    total_content_size_mb: float = 0.0
    
    # File lists
    processed_file_ids: List[str] = field(default_factory=list)
    skipped_file_ids: List[str] = field(default_factory=list)
    failed_file_ids: List[str] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Success rate as percentage."""
        if self.total_files == 0:
            return 0.0
        return (self.files_processed / self.total_files) * 100.0
    
    @property
    def total_entities(self) -> int:
        """Total entities from KG processing."""
        return self.kg_result.total_entities if self.kg_result else 0
    
    @property
    def total_relationships(self) -> int:
        """Total relationships from KG processing."""
        return self.kg_result.total_relationships if self.kg_result else 0
    
    @property
    def total_chunks(self) -> int:
        """Total chunks from KG processing."""
        return self.kg_result.total_chunks if self.kg_result else 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "category_name": self.category_name,
            "success": self.success,
            "error_message": self.error_message,
            "warnings": self.warnings,
            "processing_time_seconds": self.processing_time_seconds,
            "total_files": self.total_files,
            "files_processed": self.files_processed,
            "files_skipped": self.files_skipped,
            "files_failed": self.files_failed,
            "success_rate": self.success_rate,
            "kg_created": self.kg_created,
            "total_entities": self.total_entities,
            "total_relationships": self.total_relationships,
            "total_chunks": self.total_chunks,
            "avg_file_size_mb": self.avg_file_size_mb,
            "total_content_size_mb": self.total_content_size_mb,
            "kg_result": self.kg_result.to_dict() if self.kg_result else None,
            "processed_file_ids": self.processed_file_ids,
            "skipped_file_ids": self.skipped_file_ids,
            "failed_file_ids": self.failed_file_ids,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None
        }


@dataclass
class OrchestratorResult(ProcessingResult):
    """
    Complete result from the orchestrator including all categories and summary stats.
    """
    # Discovery stats
    total_files_discovered: int = 0
    total_folders_scanned: int = 0
    
    # Categorization stats
    total_files_categorized: int = 0
    total_categories: int = 0
    uncategorized_files: int = 0
    
    # Processing stats
    categories_processed: int = 0
    knowledge_graphs_created: int = 0
    
    # Detailed results
    category_results: Dict[str, CategoryProcessingResult] = field(default_factory=dict)
    file_categories: Dict[str, List[str]] = field(default_factory=dict)  # file_id -> category_names
    
    # Summary statistics
    total_entities_created: int = 0
    total_relationships_created: int = 0
    total_chunks_created: int = 0
    
    # Configuration used
    config_snapshot: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def total_files(self) -> int:
        """Total files processed across all categories."""
        return sum(result.total_files for result in self.category_results.values())
    
    @property
    def overall_success_rate(self) -> float:
        """Overall success rate across all categories."""
        total_files = self.total_files
        if total_files == 0:
            return 0.0
        
        successful_files = sum(result.files_processed for result in self.category_results.values())
        return (successful_files / total_files) * 100.0
    
    def get_category_summary(self) -> Dict[str, Dict[str, Any]]:
        """Get summary of all categories."""
        summary = {}
        for category_name, result in self.category_results.items():
            summary[category_name] = {
                "file_count": result.total_files,
                "success_rate": result.success_rate,
                "kg_created": result.kg_created,
                "entities": result.total_entities,
                "relationships": result.total_relationships,
                "chunks": result.total_chunks
            }
        return summary
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "success": self.success,
            "error_message": self.error_message,
            "warnings": self.warnings,
            "processing_time_seconds": self.processing_time_seconds,
            "total_files_discovered": self.total_files_discovered,
            "total_folders_scanned": self.total_folders_scanned,
            "total_files_categorized": self.total_files_categorized,
            "total_categories": self.total_categories,
            "uncategorized_files": self.uncategorized_files,
            "categories_processed": self.categories_processed,
            "knowledge_graphs_created": self.knowledge_graphs_created,
            "total_files": self.total_files,
            "overall_success_rate": self.overall_success_rate,
            "total_entities_created": self.total_entities_created,
            "total_relationships_created": self.total_relationships_created,
            "total_chunks_created": self.total_chunks_created,
            "category_results": {name: result.to_dict() for name, result in self.category_results.items()},
            "category_summary": self.get_category_summary(),
            "file_categories": self.file_categories,
            "config_snapshot": self.config_snapshot,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None
        }
