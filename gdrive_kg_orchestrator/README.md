# Google Drive Knowledge Graph Orchestrator

A comprehensive system that traverses Google Drive folders, categorizes files, and creates knowledge graphs for each category using the enterprise_kg_minimal system.

## 🎯 Overview

This project combines Google Drive traversal capabilities with enterprise knowledge graph creation to:

1. **Traverse Google Drive**: Recursively explore folders and list all files
2. **Categorize Files**: Classify files by type, content, and metadata
3. **Create Knowledge Graphs**: Process each category using enterprise_kg_minimal
4. **Track Progress**: Monitor processing status and handle errors gracefully

## 🏗️ Architecture

```
gdrive_kg_orchestrator/
├── README.md                          # This file
├── requirements.txt                   # Dependencies
├── config/                           # Configuration files
│   ├── __init__.py
│   ├── settings.py                   # Configuration management
│   └── categories.yaml               # File categorization rules
├── core/                            # Core processing modules
│   ├── __init__.py
│   ├── drive_traverser.py           # Google Drive traversal
│   ├── file_categorizer.py          # File classification
│   ├── category_manager.py          # Category organization
│   └── kg_orchestrator.py           # Knowledge graph coordination
├── models/                          # Data models
│   ├── __init__.py
│   ├── file_info.py                 # File metadata models
│   ├── category.py                  # Category models
│   └── processing_result.py         # Result models
├── utils/                           # Utility functions
│   ├── __init__.py
│   ├── drive_auth.py                # Google Drive authentication
│   ├── file_utils.py                # File processing utilities
│   └── progress_tracker.py          # Progress monitoring
├── examples/                        # Usage examples
│   ├── __init__.py
│   ├── basic_usage.py               # Simple traversal example
│   └── advanced_categorization.py   # Advanced categorization
└── tests/                           # Test files
    ├── __init__.py
    ├── test_traverser.py
    ├── test_categorizer.py
    └── test_orchestrator.py
```

## 🚀 Key Features

### 1. **Google Drive Traversal**
- Recursive folder exploration
- File metadata extraction
- Batch processing capabilities
- Rate limiting and error handling

### 2. **Intelligent File Categorization**
- **By File Type**: Documents, Images, Videos, etc.
- **By Content**: Technical docs, Reports, Presentations
- **By Metadata**: Creation date, size, sharing permissions
- **By Folder Structure**: Department, Project, Date-based
- **Custom Rules**: User-defined categorization logic

### 3. **Knowledge Graph Creation**
- Category-based processing using enterprise_kg_minimal
- Parallel processing for multiple categories
- Progress tracking and error recovery
- Detailed processing reports

### 4. **Configuration Management**
- YAML-based categorization rules
- Environment-based configuration
- Flexible category definitions
- Custom processing parameters

## 📋 Categorization Strategies

### Built-in Categories:
1. **Technical Documentation**: `.md`, `.rst`, `.txt` files with technical content
2. **Business Reports**: `.pdf`, `.docx` files with business content
3. **Presentations**: `.pptx`, `.pdf` presentation files
4. **Spreadsheets**: `.xlsx`, `.csv` data files
5. **Code Documentation**: Files in code repositories
6. **Meeting Notes**: Files with meeting-related content
7. **Project Files**: Files organized by project folders
8. **Department Files**: Files organized by department structure

### Custom Categories:
- Define your own rules in `config/categories.yaml`
- Combine multiple criteria (file type + content + metadata)
- Use regex patterns for flexible matching
- Set processing priorities and parameters

## 🔧 Installation & Setup

### 1. Install Dependencies
```bash
cd gdrive_kg_orchestrator
pip install -r requirements.txt
```

### 2. Configure Authentication
```bash
# Copy your service account JSON file
cp /path/to/your/service_account.json config/service_account.json
```

### 3. Set Environment Variables
```bash
# Create .env file
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password
OPENAI_API_KEY=your_openai_key
GOOGLE_DRIVE_ROOT_FOLDER_ID=your_root_folder_id
```

### 4. Configure Categories
Edit `config/categories.yaml` to define your categorization rules.

## 📖 Usage Examples

### Basic Usage
```python
from gdrive_kg_orchestrator import DriveKGOrchestrator

# Initialize orchestrator
orchestrator = DriveKGOrchestrator(
    service_account_path="config/service_account.json",
    root_folder_id="your_folder_id"
)

# Traverse and categorize files
result = orchestrator.process_drive_folder()

# Check results
print(f"Found {result.total_files} files in {result.total_categories} categories")
for category, files in result.categories.items():
    print(f"Category '{category}': {len(files)} files")
```

### Advanced Categorization
```python
# Custom categorization rules
custom_rules = {
    "ai_research": {
        "file_extensions": [".pdf", ".docx"],
        "content_keywords": ["artificial intelligence", "machine learning", "neural network"],
        "folder_patterns": ["**/AI/**", "**/Research/**"],
        "min_file_size": 1024  # bytes
    }
}

orchestrator = DriveKGOrchestrator(
    service_account_path="config/service_account.json",
    root_folder_id="your_folder_id",
    custom_categories=custom_rules
)

# Process with custom rules
result = orchestrator.process_drive_folder(
    create_knowledge_graphs=True,
    parallel_processing=True,
    max_workers=4
)
```

### Category-Specific Processing
```python
# Process only specific categories
result = orchestrator.process_categories(
    categories=["technical_docs", "business_reports"],
    kg_config={
        "chunking_strategy": "hybrid",
        "chunk_size": 1000,
        "llm_provider": "openai",
        "llm_model": "gpt-4o"
    }
)
```

## 📊 Processing Results

The system returns comprehensive results:

```python
{
    "success": True,
    "total_files": 1250,
    "total_categories": 8,
    "processing_time_seconds": 3600,
    "categories": {
        "technical_docs": {
            "file_count": 45,
            "kg_created": True,
            "chunks_created": 234,
            "entities_extracted": 1456,
            "relationships_created": 892
        },
        "business_reports": {
            "file_count": 23,
            "kg_created": True,
            "chunks_created": 156,
            "entities_extracted": 789,
            "relationships_created": 445
        }
        # ... more categories
    },
    "errors": [],
    "skipped_files": []
}
```

## 🔍 Monitoring & Progress Tracking

- Real-time progress updates
- Detailed logging for debugging
- Error recovery and retry mechanisms
- Processing statistics and metrics
- Export results to various formats (JSON, CSV, Excel)

## 🎛️ Configuration Options

### Drive Traversal
- Maximum depth for folder traversal
- File size limits and filters
- File type inclusion/exclusion
- Rate limiting for API calls

### Categorization
- Custom category definitions
- Priority-based processing
- Content analysis depth
- Metadata extraction options

### Knowledge Graph Creation
- Per-category KG parameters
- Parallel processing settings
- Error handling strategies
- Output format options

## 🔗 Integration

This orchestrator integrates seamlessly with:
- **gdrive_conn**: For Google Drive authentication and basic file access
- **enterprise_kg_minimal**: For knowledge graph creation and storage
- **External systems**: Via REST API or direct Python integration

## 📈 Scalability

- Batch processing for large folder structures
- Parallel category processing
- Incremental updates for changed files
- Distributed processing capabilities
- Memory-efficient streaming for large files

## 🛠️ Development

### Running Tests
```bash
python -m pytest tests/
```

### Adding Custom Categories
1. Define rules in `config/categories.yaml`
2. Implement custom logic in `core/file_categorizer.py`
3. Add tests in `tests/test_categorizer.py`

### Extending Functionality
- Add new file type handlers
- Implement custom content analyzers
- Create specialized KG processing pipelines
- Add new output formats

## 📄 License

This project builds upon the gdrive_conn and enterprise_kg_minimal systems.
