"""
Hybrid Search Engine for Enterprise KG

This module provides hybrid search capabilities that combine vector similarity search
with graph-based retrieval (GraphRAG) for enhanced information discovery.

The search engine takes chunk indices (typically from Pinecone vector search) and
enriches them with graph-based context using Neo4j knowledge graph traversal.
"""

from .hybrid_search_engine import HybridSearchEngine
from .graph_rag import GraphRAG
from .search_strategies import (
    EntityCentricStrategy,
    RelationshipCentricStrategy,
    ChunkExpansionStrategy,
    HierarchicalStrategy
)
from .result_aggregator import SearchResultAggregator
from .search_schemas import (
    SearchQuery,
    SearchResult,
    GraphContext,
    EntityMatch,
    RelationshipMatch
)

__all__ = [
    'HybridSearchEngine',
    'GraphRAG',
    'EntityCentricStrategy',
    'RelationshipCentricStrategy', 
    'ChunkExpansionStrategy',
    'HierarchicalStrategy',
    'SearchResultAggregator',
    'SearchQuery',
    'SearchResult',
    'GraphContext',
    'EntityMatch',
    'RelationshipMatch'
]

# Version info
__version__ = "1.0.0"
__author__ = "Enterprise KG Team"
