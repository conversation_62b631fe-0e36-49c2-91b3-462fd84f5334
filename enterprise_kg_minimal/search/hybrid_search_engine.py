"""
Hybrid Search Engine for Enterprise KG

This module provides the main hybrid search engine that combines vector similarity search
results (chunk indices) with graph-based retrieval (GraphRAG) to deliver comprehensive
and contextually rich search results.
"""

import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime

from ..storage.neo4j_client import Neo4jClient
from .graph_rag import GraphRAG
from .search_strategies import (
    EntityCentricStrategy,
    RelationshipCentricStrategy,
    ChunkExpansionStrategy,
    HierarchicalStrategy
)
from .result_aggregator import SearchResultAggregator
from .search_schemas import (
    SearchQuery,
    SearchResult,
    SearchStrategy,
    SearchMetrics
)
from ..constants.entities import get_all_entity_types, get_entity_category_mapping, get_organizational_hierarchy_types
from ..constants.relationships import get_all_relationship_types, get_relationship_category_mapping

logger = logging.getLogger(__name__)


class HybridSearchEngine:
    """
    Main hybrid search engine that combines vector search with GraphRAG.
    
    This engine takes chunk indices (typically from Pinecone vector similarity search)
    and enriches them with graph-based context using Neo4j knowledge graph traversal.
    All entity and relationship types are dynamically loaded from the constants module.
    """
    
    def __init__(self, neo4j_client: Neo4jClient):
        """
        Initialize the hybrid search engine.
        
        Args:
            neo4j_client: Neo4j client for graph operations
        """
        self.neo4j_client = neo4j_client
        self.graph_rag = GraphRAG(neo4j_client)
        self.result_aggregator = SearchResultAggregator()
        
        # Initialize strategies
        self.strategies = {
            SearchStrategy.ENTITY_CENTRIC: EntityCentricStrategy(self.graph_rag),
            SearchStrategy.RELATIONSHIP_CENTRIC: RelationshipCentricStrategy(self.graph_rag),
            SearchStrategy.CHUNK_EXPANSION: ChunkExpansionStrategy(self.graph_rag),
            SearchStrategy.HIERARCHICAL: HierarchicalStrategy(self.graph_rag)
        }
        
        # Load available types from constants
        self.available_entity_types = get_all_entity_types()
        self.available_relationship_types = get_all_relationship_types()
        self.entity_categories = get_entity_category_mapping()
        self.relationship_categories = get_relationship_category_mapping()
        
        logger.info(f"HybridSearchEngine initialized with {len(self.available_entity_types)} entity types "
                   f"and {len(self.available_relationship_types)} relationship types")
    
    def search(
        self,
        chunk_indices: List[str],
        query_text: Optional[str] = None,
        strategy: SearchStrategy = SearchStrategy.HYBRID,
        max_results: int = 50,
        expansion_depth: int = 2,
        entity_types: Optional[Set[str]] = None,
        relationship_types: Optional[Set[str]] = None,
        entity_categories: Optional[Set[str]] = None,
        min_confidence_score: float = 0.3,
        include_chunk_context: bool = True,
        include_file_context: bool = True,
        boost_recent_entities: bool = True,
        boost_high_importance: bool = True
    ) -> SearchResult:
        """
        Execute hybrid search with the given parameters.
        
        Args:
            chunk_indices: List of chunk IDs from vector similarity search (e.g., from Pinecone)
            query_text: Optional query text for additional context
            strategy: Search strategy to use
            max_results: Maximum number of results to return
            expansion_depth: Maximum depth for graph traversal
            entity_types: Optional set of entity types to filter by
            relationship_types: Optional set of relationship types to filter by
            entity_categories: Optional set of entity categories to filter by
            min_confidence_score: Minimum confidence score for relationships
            include_chunk_context: Whether to include chunk context in results
            include_file_context: Whether to include file context in results
            boost_recent_entities: Whether to boost recently created entities
            boost_high_importance: Whether to boost high-importance entities
            
        Returns:
            SearchResult with graph-enriched information
        """
        start_time = datetime.now()
        
        try:
            # Validate inputs
            if not chunk_indices:
                logger.warning("No chunk indices provided for search")
                return self._create_empty_result(start_time)
            
            # Validate and filter entity/relationship types
            validated_entity_types = self._validate_entity_types(entity_types)
            validated_relationship_types = self._validate_relationship_types(relationship_types)
            
            # Create search query
            search_query = SearchQuery(
                chunk_indices=chunk_indices,
                query_text=query_text,
                strategy=strategy,
                max_results=max_results,
                expansion_depth=expansion_depth,
                entity_types=validated_entity_types,
                relationship_types=validated_relationship_types,
                entity_categories=entity_categories,
                min_confidence_score=min_confidence_score,
                include_chunk_context=include_chunk_context,
                include_file_context=include_file_context,
                boost_recent_entities=boost_recent_entities,
                boost_high_importance=boost_high_importance
            )
            
            # Execute search based on strategy
            if strategy == SearchStrategy.HYBRID:
                result = self._execute_hybrid_search(search_query)
            else:
                result = self._execute_single_strategy_search(search_query)
            
            # Log search completion
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds() * 1000
            
            logger.info(f"Hybrid search completed in {total_time:.2f}ms: "
                       f"{result.total_results} results, "
                       f"strategy={strategy.value}, "
                       f"chunks={len(chunk_indices)}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            return self._create_error_result(str(e), start_time)
    
    def search_entity_neighborhood(
        self,
        entity_name: str,
        max_depth: int = 2,
        entity_types: Optional[Set[str]] = None,
        relationship_types: Optional[Set[str]] = None,
        max_results: int = 50
    ) -> SearchResult:
        """
        Search for the neighborhood around a specific entity.
        
        Args:
            entity_name: Name of the central entity
            max_depth: Maximum traversal depth
            entity_types: Optional entity type filters
            relationship_types: Optional relationship type filters
            max_results: Maximum number of results
            
        Returns:
            SearchResult with entity neighborhood information
        """
        start_time = datetime.now()
        
        try:
            # Validate types
            validated_entity_types = self._validate_entity_types(entity_types)
            validated_relationship_types = self._validate_relationship_types(relationship_types)
            
            # Get neighborhood from GraphRAG
            graph_context = self.graph_rag.get_entity_neighborhood(
                entity_name=entity_name,
                max_depth=max_depth,
                entity_types=validated_entity_types,
                relationship_types=validated_relationship_types
            )
            
            # Create search query for context
            search_query = SearchQuery(
                chunk_indices=[],  # Not applicable for neighborhood search
                query_text=f"neighborhood of {entity_name}",
                strategy=SearchStrategy.ENTITY_CENTRIC,
                max_results=max_results,
                expansion_depth=max_depth,
                entity_types=validated_entity_types,
                relationship_types=validated_relationship_types
            )
            
            # Calculate quality scores
            coverage_score = len(graph_context.entity_types_found) / max(1, len(self.available_entity_types))
            coherence_score = len(graph_context.relationships) / max(1, len(graph_context.entities)) if graph_context.entities else 0
            relevance_score = 1.0 if graph_context.entities else 0.0
            
            # Create result
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds() * 1000
            
            result = SearchResult(
                query=search_query,
                graph_context=graph_context,
                total_results=len(graph_context.entities) + len(graph_context.relationships),
                processing_time_ms=processing_time,
                strategy_used=SearchStrategy.ENTITY_CENTRIC,
                coverage_score=coverage_score,
                coherence_score=min(1.0, coherence_score),
                relevance_score=relevance_score,
                debug_info={
                    "search_type": "entity_neighborhood",
                    "central_entity": entity_name,
                    "max_depth": max_depth
                }
            )
            
            logger.info(f"Entity neighborhood search for '{entity_name}' completed: "
                       f"{len(graph_context.entities)} entities, "
                       f"{len(graph_context.relationships)} relationships")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in entity neighborhood search for '{entity_name}': {e}")
            return self._create_error_result(str(e), start_time)
    
    def get_available_types(self) -> Dict[str, Any]:
        """
        Get all available entity and relationship types from constants.
        
        Returns:
            Dictionary with available types and categories
        """
        return {
            "entity_types": list(self.available_entity_types),
            "relationship_types": list(self.available_relationship_types),
            "entity_categories": list(self.entity_categories.keys()),
            "relationship_categories": list(self.relationship_categories.keys()),
            "total_entity_types": len(self.available_entity_types),
            "total_relationship_types": len(self.available_relationship_types)
        }
    
    def validate_search_parameters(
        self,
        chunk_indices: List[str],
        entity_types: Optional[Set[str]] = None,
        relationship_types: Optional[Set[str]] = None,
        expansion_depth: int = 2,
        max_results: int = 50
    ) -> Dict[str, Any]:
        """
        Validate search parameters and return validation results.
        
        Args:
            chunk_indices: Chunk indices to validate
            entity_types: Entity types to validate
            relationship_types: Relationship types to validate
            expansion_depth: Expansion depth to validate
            max_results: Max results to validate
            
        Returns:
            Dictionary with validation results
        """
        validation_result = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "suggestions": []
        }
        
        # Validate chunk indices
        if not chunk_indices:
            validation_result["errors"].append("No chunk indices provided")
            validation_result["valid"] = False
        elif len(chunk_indices) > 100:
            validation_result["warnings"].append(f"Large number of chunk indices ({len(chunk_indices)}). Consider limiting for better performance.")
        
        # Validate entity types
        if entity_types:
            invalid_entity_types = entity_types - self.available_entity_types
            if invalid_entity_types:
                validation_result["warnings"].append(f"Unknown entity types: {invalid_entity_types}")
                validation_result["suggestions"].append(f"Available entity types: {list(self.available_entity_types)[:10]}...")
        
        # Validate relationship types
        if relationship_types:
            invalid_relationship_types = relationship_types - self.available_relationship_types
            if invalid_relationship_types:
                validation_result["warnings"].append(f"Unknown relationship types: {invalid_relationship_types}")
                validation_result["suggestions"].append(f"Available relationship types: {list(self.available_relationship_types)[:10]}...")
        
        # Validate expansion depth
        if expansion_depth < 1:
            validation_result["errors"].append("Expansion depth must be at least 1")
            validation_result["valid"] = False
        elif expansion_depth > 5:
            validation_result["warnings"].append("High expansion depth may impact performance")
        
        # Validate max results
        if max_results < 1:
            validation_result["errors"].append("Max results must be at least 1")
            validation_result["valid"] = False
        elif max_results > 1000:
            validation_result["warnings"].append("High max results may impact performance")
        
        return validation_result

    def _execute_hybrid_search(self, query: SearchQuery) -> SearchResult:
        """
        Execute hybrid search using multiple strategies and aggregate results.

        Args:
            query: Search query to execute

        Returns:
            Aggregated SearchResult
        """
        try:
            # Execute multiple strategies in parallel (or sequentially for simplicity)
            strategy_results = []

            # Always include chunk expansion for hybrid search
            chunk_result = self.strategies[SearchStrategy.CHUNK_EXPANSION].execute_search(query)
            strategy_results.append(chunk_result)

            # Add entity-centric search if we have entity filters or high-importance entities
            if query.entity_types or query.boost_high_importance:
                entity_result = self.strategies[SearchStrategy.ENTITY_CENTRIC].execute_search(query)
                strategy_results.append(entity_result)

            # Add relationship-centric search if we have relationship filters
            if query.relationship_types:
                relationship_result = self.strategies[SearchStrategy.RELATIONSHIP_CENTRIC].execute_search(query)
                strategy_results.append(relationship_result)

            # Add hierarchical search if we have organizational entities
            organizational_entities = get_organizational_hierarchy_types()
            if not query.entity_types or (query.entity_types & organizational_entities):
                hierarchical_result = self.strategies[SearchStrategy.HIERARCHICAL].execute_search(query)
                strategy_results.append(hierarchical_result)

            # Aggregate results
            aggregated_result = self.result_aggregator.aggregate_results(strategy_results, query)

            logger.debug(f"Hybrid search executed {len(strategy_results)} strategies")
            return aggregated_result

        except Exception as e:
            logger.error(f"Error in hybrid search execution: {e}")
            return self._create_error_result(str(e), datetime.now())

    def _execute_single_strategy_search(self, query: SearchQuery) -> SearchResult:
        """
        Execute search using a single strategy.

        Args:
            query: Search query to execute

        Returns:
            SearchResult from the specified strategy
        """
        try:
            if query.strategy not in self.strategies:
                raise ValueError(f"Unknown search strategy: {query.strategy}")

            strategy = self.strategies[query.strategy]
            result = strategy.execute_search(query)

            logger.debug(f"Single strategy search executed: {query.strategy.value}")
            return result

        except Exception as e:
            logger.error(f"Error in single strategy search: {e}")
            return self._create_error_result(str(e), datetime.now())

    def _validate_entity_types(self, entity_types: Optional[Set[str]]) -> Optional[Set[str]]:
        """
        Validate and filter entity types against available types.

        Args:
            entity_types: Entity types to validate

        Returns:
            Validated entity types or None
        """
        if not entity_types:
            return None

        # Filter to only include valid entity types
        valid_types = entity_types & self.available_entity_types

        if len(valid_types) != len(entity_types):
            invalid_types = entity_types - self.available_entity_types
            logger.warning(f"Filtered out invalid entity types: {invalid_types}")

        return valid_types if valid_types else None

    def _validate_relationship_types(self, relationship_types: Optional[Set[str]]) -> Optional[Set[str]]:
        """
        Validate and filter relationship types against available types.

        Args:
            relationship_types: Relationship types to validate

        Returns:
            Validated relationship types or None
        """
        if not relationship_types:
            return None

        # Filter to only include valid relationship types
        valid_types = relationship_types & self.available_relationship_types

        if len(valid_types) != len(relationship_types):
            invalid_types = relationship_types - self.available_relationship_types
            logger.warning(f"Filtered out invalid relationship types: {invalid_types}")

        return valid_types if valid_types else None

    def _create_empty_result(self, start_time: datetime) -> SearchResult:
        """Create an empty search result."""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000

        from .search_schemas import GraphContext

        return SearchResult(
            query=SearchQuery(chunk_indices=[]),
            graph_context=GraphContext(),
            total_results=0,
            processing_time_ms=processing_time,
            strategy_used=SearchStrategy.HYBRID,
            debug_info={"error": "no_chunk_indices"}
        )

    def _create_error_result(self, error: str, start_time: datetime) -> SearchResult:
        """Create an error search result."""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000

        from .search_schemas import GraphContext

        return SearchResult(
            query=SearchQuery(chunk_indices=[]),
            graph_context=GraphContext(),
            total_results=0,
            processing_time_ms=processing_time,
            strategy_used=SearchStrategy.HYBRID,
            debug_info={"error": error}
        )


# Convenience functions for easy integration

def create_hybrid_search_engine(
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j",
    neo4j_password: str = "password",
    neo4j_database: Optional[str] = None
) -> HybridSearchEngine:
    """
    Create a hybrid search engine with default Neo4j connection.

    Args:
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        neo4j_database: Optional Neo4j database name

    Returns:
        Configured HybridSearchEngine instance
    """
    from ..storage.neo4j_client import Neo4jConnection, Neo4jClient

    connection = Neo4jConnection(
        uri=neo4j_uri,
        user=neo4j_user,
        password=neo4j_password,
        database=neo4j_database
    )

    neo4j_client = Neo4jClient(connection)
    return HybridSearchEngine(neo4j_client)


def search_with_chunk_indices(
    chunk_indices: List[str],
    query_text: Optional[str] = None,
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j",
    neo4j_password: str = "password",
    strategy: SearchStrategy = SearchStrategy.HYBRID,
    max_results: int = 50,
    expansion_depth: int = 2,
    entity_types: Optional[Set[str]] = None,
    relationship_types: Optional[Set[str]] = None
) -> SearchResult:
    """
    Convenience function to perform hybrid search with chunk indices.

    This function simulates the typical workflow where chunk indices are obtained
    from a vector similarity search (e.g., Pinecone) and then enriched with graph context.

    Args:
        chunk_indices: List of chunk IDs from vector search
        query_text: Optional query text for context
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        strategy: Search strategy to use
        max_results: Maximum number of results
        expansion_depth: Graph traversal depth
        entity_types: Optional entity type filters
        relationship_types: Optional relationship type filters

    Returns:
        SearchResult with graph-enriched information
    """
    # Create search engine
    search_engine = create_hybrid_search_engine(
        neo4j_uri=neo4j_uri,
        neo4j_user=neo4j_user,
        neo4j_password=neo4j_password
    )

    # Execute search
    try:
        result = search_engine.search(
            chunk_indices=chunk_indices,
            query_text=query_text,
            strategy=strategy,
            max_results=max_results,
            expansion_depth=expansion_depth,
            entity_types=entity_types,
            relationship_types=relationship_types
        )
        return result
    finally:
        # Clean up connection
        search_engine.neo4j_client.close()
